# frozen_string_literal: true

class Models::Organization < Models::AbstractModel
  # Use ParanoiaElastic, data still exist in elastic after soft delete
  include Models::AbstractParanoiaElastic
  include Models::AbstractConcern

  mount_uploader :avatar, Repositories::Uploaders::OrganizationAvatarUploader

  store_accessor :settings, :organization_uniq_id
  store_accessor :settings, :user_uniq_id
  store_accessor :settings, :waba_id

  ## settings flag
  store_accessor :settings, :auto_assign_agent
  store_accessor :settings, :agent_can_takeover
  store_accessor :settings, :is_override_contact_by_upload
  store_accessor :settings, :agent_can_assign_agent
  store_accessor :settings, :auto_responder_in_office_hours
  store_accessor :settings, :auto_responder_out_office_hours
  store_accessor :settings, :agent_can_send_broadcast
  store_accessor :settings, :round_robin_all_agent
  store_accessor :settings, :round_robin_online_agent
  store_accessor :settings, :limit_ongoing_chat
  store_accessor :settings, :app_type
  store_accessor :settings, :billing_code
  store_accessor :settings, :is_queue_round_robin
  store_accessor :settings, :is_contact_masking
  store_accessor :settings, :broadcast_direct_limit
  store_accessor :settings, :broadcast_direct_interval
  store_accessor :settings, :broadcast_list_limit
  store_accessor :settings, :broadcast_list_interval
  store_accessor :settings, :contact_list_upload
  store_accessor :settings, :bot_can_settings_webhook
  store_accessor :settings, :limit_ongoing_chat_agent
  store_accessor :settings, :inbox_pooling
  store_accessor :settings, :chatbot_automation
  store_accessor :settings, :mqtt_notification
  store_accessor :settings, :whitelist_renewal_v1
  store_accessor :settings, :chatbot_score_card_custom_org
  store_accessor :settings, :sla_dashboard_enabled
  store_accessor :settings, :chatbot_webhook_resolve
  store_accessor :settings, :chatbot_conversation_summary
  store_accessor :settings, :qontak_channel_v3
  store_accessor :settings, :enable_device_limit
  store_accessor :settings, :revamp_inbox
  store_accessor :settings, :is_enable_custom_view

  ## settings webhook

  # Agent allocation settings
  ## Auto Agent Allocation V2
  store_accessor :settings, :auto_agent_allocation
  ### Assign New Room
  AAA_ASSIGN_NEW_ROOM = 'assign_new_room'
  public_constant :AAA_ASSIGN_NEW_ROOM

  ## custom_agent_allocation
  store_accessor :settings, :custom_agent_allocation

  # Agent assigned / unassigned
  # agent_responsibility
  store_accessor :settings, :agent_assigned_to_room
  store_accessor :settings, :agent_unassigned_from_room
  store_accessor :settings, :agent_handover_room

  # rooms event
  # room_interaction
  store_accessor :settings, :room_resolved
  store_accessor :settings, :room_created

  # messages event
  # message_interaction
  store_accessor :settings, :receive_message_from_agent
  store_accessor :settings, :receive_message_from_customer
  store_accessor :settings, :status_message
  store_accessor :settings, :broadcast_log_status

  # custom settings
  store_accessor :settings, :segment
  store_accessor :settings, :segment_value
  store_accessor :settings, :include_prev_message_broadcast

  # agent idle rule assignment
  store_accessor :settings, :agent_idle_rule_assignment

  # waba event
  # waba_interaction
  store_accessor :settings, :status_template

  # auto resolve flag
  store_accessor :auto_resolve, :is_active
  store_accessor :auto_resolve, :period
  store_accessor :auto_resolve, :type
  store_accessor :auto_resolve, :auto_resolve_tag

  # auto resolve flag
  store_accessor :auto_resolve_comment, :is_active
  store_accessor :auto_resolve_comment, :period
  store_accessor :auto_resolve_comment, :type
  store_accessor :auto_resolve_comment, :auto_resolve_tag

  # chatbot event
  # status agent
  store_accessor :settings, :available_user_status

  # broadcast interval
  store_accessor :settings, :broadcast_interval

  # contact list upload limit
  store_accessor :settings, :contact_list_upload_csv
  store_accessor :settings, :contact_list_upload_excel

  # custom webhook settings
  store_accessor :settings, :high_sla_webhooks
  store_accessor :settings, :dynamic_retry_webhooks
  store_accessor :settings, :dynamic_timeout_webhooks

  # mekari webhook settings
  store_accessor :settings, :mekari_room_webhooks

  # security settings
  store_accessor :settings, :create_message_template_otp

  # internal cluster flag
  store_accessor :settings, :internal_cluster
  store_accessor :settings, :internal_cluster_redirect

  # idle customer settings
  store_accessor :settings, :enable_idle_customers_global
  store_accessor :settings, :enable_idle_customers_specific
  store_accessor :settings, :idle_customers_message
  store_accessor :settings, :idle_customers_period

  # campaign plan flag
  store_accessor :settings, :enable_campaign_plan

  # wa flow flag
  store_accessor :settings, :enable_wa_flow_specific

  has_many :auths, as: :auth_able
  has_many :preferences_sources,
           as:           :preferences_able,
           class_name:   'Models::PreferencesSource',
           foreign_key:  'preferences_able_id',
           foreign_type: 'preferences_able_type',
           dependent:    :destroy

  has_many :preferences, through: :preferences_sources
  has_many :rooms,
           class_name:  'Models::Room',
           foreign_key: 'organization_id',
           dependent:   :destroy

  has_many :users,
           class_name:  'Models::User',
           foreign_key: 'organization_id',
           dependent:   :destroy

  has_many :message_templates,
           class_name:  'Models::MessageTemplate',
           foreign_key: 'organization_id',
           dependent:   :destroy

  has_many :message_broadcasts,
           class_name:  'Models::MessageBroadcast',
           foreign_key: 'organization_id',
           dependent:   :destroy

  has_many :channel_integrations,
           class_name:  'Models::ChannelIntegration',
           foreign_key: 'organization_id',
           dependent:   :destroy,
           inverse_of:  :organization

  has_many :contact_lists,
           class_name: 'Models::ContactList',
           dependent:  :destroy

  has_many :webhooks,
           class_name:  'Models::Webhook',
           foreign_key: 'organization_id',
           dependent:   :destroy

  has_many :business_hours,
           class_name:  'Models::BusinessHour',
           foreign_key: 'organization_id',
           dependent:   :destroy

  has_many :contact_custom_fields,
           class_name:  'Models::ContactCustomField',
           foreign_key: 'organization_id',
           dependent:   :destroy

  has_many :widgets,
           class_name:  'Models::Widget',
           foreign_key: 'organization_id',
           dependent:   :destroy

  has_one :package,
          class_name:  'Models::Billing::OrganizationPackage',
          foreign_key: 'organization_id'
  has_many :meta_events,
           class_name:  'Models::MetaConversionEventLog',
           foreign_key: 'organization_id'

  has_many :user_surveys, class_name: 'Models::UserSurvey', foreign_key: 'organization_id'

  def build_store_accessors!(field_names = [], parent_col: :settings)
    field_names.each { |field_name| add_store_accessor(field_name, parent_col: parent_col) }
  end

  def add_store_accessor(field_name, parent_col: :settings)
    singleton_class.class_eval { store_accessor parent_col, field_name }
  end

  def flipper_id
    id.to_s
  end

  def billing_enabled?
    settings['billing_enabled'].eql?(true)
  end

  def enable_billing
    settings['billing_enabled'] = true
    save
  end

  def disable_billing
    settings['billing_enabled'] = false
    save
  end

  def default_broadcast_interval
    settings['broadcast_interval'] || 300 # in second
  end

  def max_upload_contact_list
    (settings['contact_list_upload'] || Services::Preference.new.info(:contact_list_upload)&.dig(:value, 'limit') || 5000).to_i
  end

  mappings do
    indexes :id, type: :keyword
    indexes :name, type: :text
    indexes :email, type: :keyword
    indexes :phone, type: :text
    indexes :response_message_in_office_hour, type: :text
    indexes :response_message_out_office_hour, type: :text
    indexes :max_ongoing_chat, type: :integer
    indexes :created_at, type: :date, format: 'strict_date_optional_time'
    indexes :updated_at, type: :date, format: 'strict_date_optional_time'
    indexes :deleted_at, type: :date, format: 'strict_date_optional_time'
    indexes :moderator_account_id, type: :keyword
    indexes :company_id, type: :keyword
    indexes :sso_id, type: :keyword
    indexes :industry, type: :text
    indexes :segment, type: :text
    indexes :auto_resolve, type: :object, dynamic: false do
      indexes :is_active, type: :boolean, null_value: false
      indexes :period, type: :integer, null_value: 0
      indexes :type, type: :text
      indexes :auto_resolve_tag, type: :text
    end
    indexes :auto_resolve_comment, type: :object, dynamic: false do
      indexes :is_active, type: :boolean, null_value: false
      indexes :period, type: :integer, null_value: 0
      indexes :type, type: :text
      indexes :auto_resolve_tag, type: :text
    end
    indexes :alerts, type: :object, dynamic: false do
      indexes :is_muv_over_soon, type: :boolean, null_value: false
      indexes :is_mcc_over_soon, type: :boolean, null_value: false
      indexes :is_muv_over, type: :boolean, null_value: false
      indexes :is_mcc_over, type: :boolean, null_value: false
      indexes :is_package_inactive, type: :boolean, null_value: false
      indexes :is_has_debt, type: :boolean, null_value: false
    end
    indexes :settings, type: :object, dynamic: false do
      indexes :waba_id, type: :keyword
      indexes :waba_name, type: :text
      indexes :waba_message_template_namespace, type: :keyword
      indexes :user_uniq_id, type: :keyword
      indexes :organization_uniq_id, type: :keyword
      indexes :auto_assign_agent, type: :boolean
      indexes :agent_can_takeover, type: :boolean
      indexes :is_override_contact_by_upload, type: :boolean
      indexes :agent_can_assign_agent, type: :boolean
      indexes :auto_responder_in_office_hours, type: :boolean
      indexes :auto_responder_out_office_hours, type: :boolean
      indexes :custom_agent_allocation, type: :keyword
      indexes :agent_assigned_to_room, type: :keyword
      indexes :agent_unassigned_from_room, type: :keyword
      indexes :agent_handover_room, type: :keyword
      indexes :receive_message_from_agent, type: :keyword
      indexes :receive_message_from_customers, type: :keyword
      indexes :status_message, type: :keyword
      indexes :broadcast_log_status, type: :keyword
      indexes :room_resolved, type: :keyword
      indexes :room_created, type: :keyword
      indexes :agent_can_send_broadcast, type: :boolean
      indexes :status_template, type: :keyword
      indexes :round_robin_all_agent, type: :boolean
      indexes :round_robin_online_agent, type: :boolean
      indexes :limit_ongoing_chat, type: :boolean
      indexes :app_type, type: :keyword
      indexes :billing_code, type: :keyword, null_value: 'NULL'
      indexes :available_user_status, type: :boolean
      indexes :billing_enabled, type: :boolean
      indexes :is_queue_round_robin, type: :boolean
      indexes :is_contact_masking, type: :boolean
      indexes :broadcast_interval, type: :integer, null_value: 0
      indexes :segment, type: :boolean
      indexes :segment_value, type: :keyword
      indexes :include_prev_message_broadcast, type: :boolean
      indexes :broadcast_direct_limit, type: :integer, null_value: 0
      indexes :broadcast_direct_interval, type: :integer, null_value: 0
      indexes :broadcast_list_limit, type: :integer, null_value: 0
      indexes :broadcast_list_interval, type: :integer, null_value: 0
      indexes :contact_list_upload, type: :integer, null_value: 5000
    end
    indexes :avatar, type: :object, dynamic: false
    indexes :block_broadcast, type: :boolean
    indexes :is_centralized_contact, type: :boolean
  end
end
