# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Models::Contact, type: :model, preference: true do
  let(:organization) { create :organization }
  let(:channel) { create :channel_wa, organization_id: organization.id }
  let(:full_name) { Faker::Name.name }
  let(:subject) {
    described_class.new(phone_number: phone_number, organization_id: organization.id, full_name: full_name, account_uniq_id: account_uniq_id, channel_integration_id: channel.id, channel: channel_type)
  }
  let(:phone_number) { '62***********' }
  let(:account_uniq_id) { '62***********' }
  let(:channel_type) { 'wa' }
  let(:column_names) {
    %w[id phone_number extra created_at updated_at organization_id full_name account_uniq_id channel_integration_id deleted_at avatar is_valid status error_messages email channel is_contact code authority contact_handler_id is_blocked is_contact_extra contact_master_id qontak_customer_id]
  }
  let(:prefix) { Rails.env[0..3] }

  context 'given valid attribute' do
    it 'should be valid column attributes' do
      expect(described_class.column_names - column_names).to eq([])
    end

    it 'should be valid with valid attributes' do
      expect(subject).to be_valid
    end

    it 'should returns valid index name' do
      expect(described_class.index_name).to eq "#{prefix}_models_contacts"
    end

    it 'should include Models::AbstractConcernBasedOnOrganizationWithRouting' do
      expect(described_class).to include Models::AbstractConcernBasedOnOrganizationWithRouting
    end

    context 'when have contact_master' do
      let(:contact_master) { create :contact_master, name: 'konohasenpu', organization_id: organization.id }

      before do
        subject.contact_master_id = contact_master.id
      end

      it 'will return contact master' do
        expect(subject.contact_master.id).to eq(contact_master.id)
      end
    end
  end

  describe '.import' do
    let(:contacts) { create_list(:contact, 3, organization: organization) }
    let(:options) { { batch_size: 1000, on_duplicate_key_update: [:contact_master_id] } }

    before do
      allow(Models::Contact).to receive(:callback_import_contact)
    end

    it 'imports records and calls callback_import_contact' do
      result = Models::Contact.import(contacts, options)
      expect(result).not_to eq(0)
      expect(Models::Contact).to have_received(:callback_import_contact)
    end

    it 'handles errors and logs them' do
      allow(Models::Contact).to receive(:import).and_raise(StandardError)
      expect { Models::Contact.import(contacts, options) }.to raise_error(StandardError)
    end
  end

  describe '.import!' do
    let(:contacts) { create_list(:contact, 3, organization: organization) }
    let(:options) { { batch_size: 1000, on_duplicate_key_update: [:contact_master_id] } }

    before do
      allow(Models::Contact).to receive(:callback_import_contact)
    end

    it 'imports records and calls callback_import_contact' do
      result = Models::Contact.import!(contacts, options)
      expect(result).not_to eq(0)
      expect(Models::Contact).to have_received(:callback_import_contact)
    end

    it 'handles errors and logs them' do
      allow(Models::Contact).to receive(:import!).and_raise(StandardError)
      expect { Models::Contact.import!(contacts, options) }.to raise_error(StandardError)
    end
  end

  describe '#record_contact_master_id' do
    let(:contact_master_id) { SecureRandom.uuid }
    let(:response) { Dry::Monads::Success(Hashie::Mash.new({ "phone_#{account_uniq_id}" => { contact_master_id: contact_master_id } })) }

    before do
      allow(CentralizedContacts::Services::GetContactMasterAdapter).to receive(:new).and_return(double(call: response))
      create_enable_preference(:callback_backfill_contact_master_id, 'callback backfill contact_master_id', extra: {}, target: 'feature', organization_ids: [organization.id])
    end

    context 'when contact master id is not present' do
      let(:cache_result) { Success({ full_name: 'Cached Name' }.with_indifferent_access) }
      let!(:contact_master) { create :contact_master, id: contact_master_id, name: full_name, organization_id: organization.id }

      before do
        allow_any_instance_of(CentralizedContacts::Services::GetContact).to receive(:call).and_return(cache_result)
        create_enable_preference(:get_full_name_contact_360, 'get full name contact 360', extra: {}, target: 'feature', organization_ids: [organization.id])
      end

      it 'sets the contact master id' do
        subject.save
        expect(subject.contact_master_id).to eq(contact_master_id)

        cm = Models::ContactMaster.find_by(id: contact_master_id)
        expect(cm.name).to eq(full_name)
      end
    end

    context 'when contact master id is present' do
      let!(:contact_master) { create :contact_master, id: contact_master_id, name: full_name, organization_id: organization.id }

      before do
        subject.contact_master_id = contact_master_id
      end

      it 'does not change the contact master id' do
        subject.save
        expect(subject.contact_master_id).to eq(contact_master_id)
      end
    end

    context 'when contact_master exists and name is different from full_name' do
      let!(:contact_master) do
        create(:contact_master, id: contact_master_id, name: 'Old Master Name', organization_id: organization.id)
      end

      before do
        subject.contact_master_id = contact_master.id
        subject.full_name = 'Updated Contact Name'
      end

      it 'updates the contact_master name to match full_name' do
        subject.save
        expect(contact_master.reload.name).to eq('Updated Contact Name')
      end
    end

    context 'when contact_master exists and name is the same as full_name' do
      let!(:contact_master) do
        create(:contact_master, id: contact_master_id, name: full_name, organization_id: organization.id)
      end

      before do
        subject.contact_master_id = contact_master.id
      end

      it 'does not update the contact_master name' do
        expect(contact_master).not_to receive(:update_column)
        subject.save
        expect(contact_master.reload.name).to eq(full_name)
      end
    end
  end

  describe '#deliver_contact_360_webhook' do
    let(:organization) { create :organization }
    let(:contact) { build :contact, organization: organization, qontak_customer_id: nil }
    let(:response) { Dry::Monads::Success(Hashie::Mash.new({ "#{channel_type}_#{account_uniq_id}" => { contact_master_id: contact_master_id } })) }
    let(:get_contact_service) { instance_double(CentralizedContacts::Services::GetContact) }

    before do
      allow(CentralizedContacts::Services::ChannelIntegration).to receive(:channel_enabled?).and_return(true)
      create_enable_preference(:callback_deliver_contact_360_webhook, 'callback deliver contact 360 webhook', extra: {}, target: 'feature', organization_ids: [organization.id])
      allow(Repositories::CentralizedContacts::DeliverWebhookWorker).to receive(:deliver)
      allow(CentralizedContacts::Services::GetContact).to receive(:new).with(contact, organization.id).and_return(get_contact_service)
    end

    context 'when qontak_customer_id is present' do
      it 'does not set the qontak customer id' do
        contact.update(qontak_customer_id: SecureRandom.uuid, extra: { username: 'username' })

        expect(Repositories::CentralizedContacts::DeliverWebhookWorker).to have_received(:deliver).with(hash_including(action: 'update'))
      end
    end

    context 'when contact is created' do
      it 'delivers create webhook' do
        contact.save
        expect(Repositories::CentralizedContacts::DeliverWebhookWorker).to have_received(:deliver).with(
                                                                             action:                 'create',
                                                                             contact_ids:            [contact.id],
                                                                             organization_id:        contact.organization_id,
                                                                             is_centralized_contact: contact.organization.is_centralized_contact
        )
      end
    end

    context 'when contact is updated' do
      before do
        contact.save
        contact.update(qontak_customer_id: nil, extra: { username: 'username' })
      end

      it 'delivers update webhook' do
        expect(Repositories::CentralizedContacts::DeliverWebhookWorker).to have_received(:deliver).with(
                                                                             action:                 'update',
                                                                             contact_ids:            [contact.id],
                                                                             organization_id:        contact.organization_id,
                                                                             is_centralized_contact: contact.organization.is_centralized_contact
        )
      end
    end
  end

  describe '#deliver_deletion_contact_360_webhook' do
    let(:organization) { create :organization }
    let(:contact) { create :contact, organization: organization }

    before do
      allow(CentralizedContacts::Services::ChannelIntegration).to receive(:channel_enabled?).and_return(true)
      create_enable_preference(:callback_deliver_contact_360_webhook, 'callback deliver contact 360 webhook', extra: {}, target: 'feature', organization_ids: [organization.id])
      allow(Repositories::CentralizedContacts::DeliverWebhookWorker).to receive(:deliver)
    end

    context 'when organization is blank' do
      it 'does not set the contact master id' do
        contact.organization_id = nil
        contact.destroy

        expect(Repositories::CentralizedContacts::DeliverWebhookWorker).not_to have_received(:deliver).with(hash_including(action: 'delete'))
      end
    end

    context 'when contact is deleted' do
      it 'delivers delete webhook' do
        expect(CentralizedContacts::Services::DeleteContactMaster).to have_received(:new).with(contact.id, contact.contact_master_id, contact.organization_id)
        contact.destroy
        expect(Repositories::CentralizedContacts::DeliverWebhookWorker).to have_received(:deliver).with(
                                                                             action:                 'delete',
                                                                             contact_ids:            [contact.id],
                                                                             organization_id:        contact.organization_id,
                                                                             is_centralized_contact: contact.organization.is_centralized_contact
        )
      end
    end
  end

  describe '#deliver_deletion_contact_360_webhook' do
    let(:organization) { create :organization }
    let(:contacts) { create_list :contact, 2, organization: organization }

    before do
      create_enable_preference(:callback_delete_contact_360_webhook, 'callback delete contact 360 webhook', extra: {}, target: 'feature', organization_ids: [organization.id])
      allow(CentralizedContacts::FillRelatedDataWorker).to receive(:perform_async)
    end

    context 'when contacts are deleted' do
      it 'delivers delete webhook' do
        expect(CentralizedContacts::DeleteWebhookWorker).to receive(:perform_async).with(
                                                              match_array(contacts.pluck(:id)), organization.id
        )
        # convert to active record relation
        contact_records = Models::Contact.where(id: contacts.pluck(:id))
        contact_records.delete_all
      end
    end
  end

  describe '#normalize_phone_number' do
    context 'when feature toggle off' do
      it 'normalizes the phone number by removing invalid characters' do
        subject.phone_number = ' 62821-2121-2211 '
        subject.valid?
        expect(subject.phone_number).to eq(' 62821-2121-2211 ')
      end

      it 'normalizes phone number with + as a phone number' do
        subject.phone_number = '+62821-2121-2211 '
        subject.valid?
        expect(subject.phone_number).to eq('+62821-2121-2211 ')
      end
    end

    context 'when feature toggle on' do
      before do
        create_enable_preference(:normalize_contact, 'normalize contact', extra: {}, target: 'feature', organization_ids: [subject.organization_id])
      end

      context 'when phone number is provided' do
        it 'normalizes the phone number by removing invalid characters' do
          subject.phone_number = ' 62821-2121-2211 '
          subject.valid?
          expect(subject.phone_number).to eq('62***********')
        end

        it 'normalizes phone number with + as a phone number' do
          subject.phone_number = '+62821-2121-2211 '
          subject.valid?
          expect(subject.phone_number).to eq('62***********')
        end
      end
    end
  end

  describe '#normalize_account_uniq_id' do
    context 'when feature toggle off' do
      context 'when channel is wa' do
        let(:channel_type) { 'wa' }

        it 'normalizes account_uniq_id as a phone number' do
          subject.account_uniq_id = ' 62821-2121-2211 '
          subject.valid?
          expect(subject.account_uniq_id).to eq(' 62821-2121-2211 ')
        end

        it 'normalizes account_uniq_id with + as a phone number' do
          subject.account_uniq_id = '+62821-2121-2211 '
          subject.valid?
          expect(subject.account_uniq_id).to eq('+62821-2121-2211 ')
        end
      end
    end

    context 'when feature toggle on' do
      before do
        create_enable_preference(:normalize_contact, 'normalize contact', extra: {}, target: 'feature', organization_ids: [subject.organization_id])
      end

      context 'when channel is wa' do
        let(:channel_type) { 'wa' }

        it 'normalizes account_uniq_id as a phone number' do
          subject.account_uniq_id = ' 62821-2121-2211 '
          subject.valid?
          expect(subject.account_uniq_id).to eq('62***********')
        end

        it 'normalizes account_uniq_id with + as a phone number' do
          subject.account_uniq_id = '+62821-2121-2211 '
          subject.valid?
          expect(subject.account_uniq_id).to eq('62***********')
        end
      end
    end
  end

  describe '#validate_phone_number_format' do
    context 'when feature toggle off' do
      context 'when phone number is valid' do
        it 'does not add any validation errors' do
          subject.phone_number = '***********'
          subject.account_uniq_id = '***********'
          subject.valid?
          expect(subject.errors).to be_empty
        end
      end

      context 'when phone number is too short' do
        it 'does not add any validation errors' do
          subject.phone_number = '123'
          subject.valid?
          expect(subject.errors).to be_empty
        end
      end

      context 'when account_uniq_id is too long' do
        it 'does not add any validation errors' do
          subject.account_uniq_id = '12345678901234567890'
          subject.valid?
          expect(subject.errors).to be_empty
        end
      end
    end

    context 'when feature toggle on' do
      before do
        create_enable_preference(:return_error_invalid_phone, 'return error invalid phone', extra: {}, target: 'feature', organization_ids: [subject.organization_id])
      end

      context 'when phone number is valid' do
        it 'does not add any validation errors' do
          subject.phone_number = '***********'
          subject.account_uniq_id = '***********'
          subject.valid?
          expect(subject.errors).to be_empty
        end
      end

      context 'when phone number is too short' do
        it 'adds an error for invalid length' do
          subject.phone_number = '123'
          subject.valid?
          expect(subject.errors[:phone_number]).to include('must be between 4 and 17 digits')
        end
      end

      context 'when account_uniq_id is too long' do
        it 'adds an error for invalid length' do
          subject.account_uniq_id = '12345678901234567890'
          subject.valid?
          expect(subject.errors[:account_uniq_id]).to include('must be between 4 and 17 digits')
        end
      end
    end
  end

  describe '.callback_import_contact' do
    let(:now) { Time.current }
    let!(:new_contact) { create(:contact, organization: organization, created_at: now, updated_at: now) }
    let!(:updated_contact) { create(:contact, organization: organization, created_at: 1.day.ago, updated_at: now) }
    let!(:imported_contacts) { [new_contact, updated_contact] }

    before do
      allow(CentralizedContacts::FillRelatedDataWorker).to receive(:perform_async)
      allow_any_instance_of(Services::Preference).to receive(:enabled?).with(:callback_import_contact_360_webhook, organization_id: organization.id).and_return(true)
    end

    it 'calls FillRelatedDataWorker' do
      described_class.callback_import_contact(imported_contacts.pluck(:id))
      expect(CentralizedContacts::FillRelatedDataWorker).to have_received(:perform_async).with(
                                                              [new_contact.id, updated_contact.id],
                                                              new_contact.organization_id
      )
    end

    it 'does not call FillRelatedDataWorker if no new or updated contacts' do
      described_class.callback_import_contact([])
      expect(CentralizedContacts::FillRelatedDataWorker).not_to have_received(:perform_async)
    end
  end

  context 'when channel deleted' do
    it 'should create room' do
      channel.destroy
      expect(subject.channel_integration_id).to eql(channel.id)
    end
  end

  describe 'elasticsearch callback' do
    let(:cache_result) { Success({ full_name: 'Cached Name' }.with_indifferent_access) }
    before do
      allow_any_instance_of(CentralizedContacts::Services::GetContact).to receive(:call).and_return(cache_result)
      allow_any_instance_of(Services::Preference).to receive(:enabled?).and_return(true)
    end
    context 'index_document' do
      let(:organization) { create :organization }
      let(:contact) { build :contact, organization: organization }

      before do
        allow_any_instance_of(Services::Preference).to receive(:enabled?).and_return(true)
        allow_any_instance_of(Services::Preference).to receive(:enabled?).with("#{Models::Contact.index_name}_multi_cluster".to_sym).and_return(false)
      end

      it 'should trigger index_document' do
        expect(contact.__elasticsearch__).to receive(:index_document).with(routing: contact.organization_id).and_call_original
        contact.save!
      end
    end

    context 'update_document' do
      let(:organization) { create :organization }
      let(:contact) { create :contact, organization: organization }

      before do
        allow_any_instance_of(Services::Preference).to receive(:enabled?).and_return(true)
        allow_any_instance_of(Services::Preference).to receive(:enabled?).with("#{Models::Contact.index_name}_multi_cluster".to_sym).and_return(false)
      end

      it 'update should trigger index_document' do
        expect(contact.__elasticsearch__).to receive(:update_document).with(routing: contact.organization_id).and_call_original
        contact.update(full_name: 'updated')
      end

      it 'touch should trigger index_document' do
        expect(contact.__elasticsearch__).to receive(:update_document).with(routing: contact.organization_id).and_call_original
        contact.full_name = 'updated'
        contact.touch
      end

      it 'save should trigger index_document' do
        expect(contact.__elasticsearch__).to receive(:update_document).with(routing: contact.organization_id).and_call_original
        contact.full_name = 'updated'
        contact.save
      end
    end

    context 'delete_document' do
      let(:organization) { create :organization }
      let(:contact) { create :contact, organization: organization }

      before do
        allow_any_instance_of(Services::Preference).to receive(:enabled?).and_return(true)
        allow_any_instance_of(Services::Preference).to receive(:enabled?).with("#{Models::Contact.index_name}_multi_cluster".to_sym).and_return(false)
      end

      it 'should trigger index_document' do
        expect(contact.__elasticsearch__).to receive(:delete_document).with(routing: contact.organization_id).and_call_original
        contact.destroy!
      end
    end
  end

  describe '#full_name' do
    let(:organization) { create :organization }
    let(:contact) { build(:contact, organization: organization, full_name: 'Original Name') }
    let(:preference_service) { instance_double(Services::Preference) }
    let(:get_contact_service) { instance_double(CentralizedContacts::Services::GetContact) }

    before do
      allow(CentralizedContacts::Services::GetContact).to receive(:new).with(contact, organization.id).and_return(get_contact_service)
      allow_any_instance_of(Services::Preference).to receive(:enabled?).and_return(true)
      allow(CentralizedContacts::Services::InvalidateContact).to receive(:new).and_return(double(call: true))
    end

    context 'when channel is unknown' do
      before { contact.update!(channel: 'unknown') }

      it 'returns the original full_name' do
        expect(contact.full_name).to eq('Original Name')
      end
    end

    context 'when select some contact column' do
      before { contact.update!(channel: 'unknown') }
      context 'when select full_name' do
        it 'returns with additional select' do
          contact2 = Models::Contact.select(:id, :full_name).find(contact.id)
          expect(contact2.organization_id).not_to be_nil
          expect(contact2.channel).not_to be_nil
        end
      end

      context 'when not select full_name' do
        it 'returns with additional select' do
          contact2 = Models::Contact.select(:id, :account_uniq_id).find(contact.id)
          expect { contact2.organization_id }.to raise_error(ActiveModel::MissingAttributeError)
          expect { contact2.channel }.to raise_error(ActiveModel::MissingAttributeError)
        end
      end
    end

    # once we deprecate full_name in database, this test should be removed
    context 'when the contact is persisted and full_name has changed' do
      let!(:contact) { create(:contact, organization: organization, full_name: 'Original Name') }

      before do
        allow(get_contact_service).to receive(:call).and_return(Dry::Monads::Success({}))
        contact.save
        contact.update(full_name: 'New Name')
        contact.instance_variable_set(:@full_name, nil)
      end

      it 'returns the updated full_name' do
        expect(contact.full_name).to eq('New Name')
      end
    end

    context 'when cache_result is successful and full_name is present' do
      before do
        allow(get_contact_service).to receive(:call).and_return(Dry::Monads::Success({ full_name: 'Cached Name' }))
      end

      it 'returns the cached full_name' do
        expect(contact.full_name).to eq('Cached Name')
      end
    end

    context 'when cache_result is successful and full_name is not present' do
      before do
        allow(get_contact_service).to receive(:call).and_return(Dry::Monads::Success({}))
      end

      it 'returns the cached full_name' do
        expect(contact.full_name).to eq('Original Name')
      end
    end

    context 'when get_full_name_contact_360 preference is disabled' do
      before do
        allow_any_instance_of(Services::Preference).to receive(:enabled?).and_return(false)
      end

      it 'returns the original full_name' do
        expect(contact.full_name).to eq('Original Name')
      end
    end

    context 'when get_full_name_contact_360 preference is enabled' do
      context 'when GetContact service succeeds' do
        let(:cache_result) { Success({ full_name: 'Cached Name' }.with_indifferent_access) }

        before do
          allow(get_contact_service).to receive(:call).and_return(cache_result)
        end

        it 'returns the cached full_name' do
          expect(contact.full_name).to eq('Cached Name')
        end
      end

      context 'when GetContact service fails' do
        let(:cache_result) { Failure('failed get contact') }

        before do
          allow(get_contact_service).to receive(:call).and_return(cache_result)
        end

        it 'returns the original full_name' do
          expect(contact.full_name).to eq('Original Name')
        end
      end
    end

    context 'when use_local_full_name is true' do
      before do
        contact.use_local_full_name = true
      end

      it 'returns the original full_name' do
        expect(contact.full_name).to eq('Original Name')
      end
    end

    context 'when use_local_full_name is false' do
      let(:cache_result) { Dry::Monads::Success(full_name: 'Cached Name') }

      before do
        contact.use_local_full_name = false
        allow(CentralizedContacts::Services::GetContact).to receive(:new).with(contact, organization.id).and_return(instance_double(CentralizedContacts::Services::GetContact, call: cache_result))
      end

      it 'returns the cached full_name if GetContact service succeeds' do
        expect(contact.full_name).to eq('Cached Name')
      end
    end
  end

  describe '#fill_contact_360_related_data' do
    let(:contact) { create(:contact) }

    context 'when skip_deliver_contact_360_webhook is true' do
      before do
        contact.skip_deliver_contact_360_webhook = true
        allow(contact).to receive(:deliver_contact_360_webhook)
        contact.fill_contact_360_related_data
      end

      it 'does not call deliver_contact_360_webhook' do
        expect(contact).not_to have_received(:deliver_contact_360_webhook)
      end
    end

    context 'when skip_deliver_contact_360_webhook is false' do
      before do
        contact.skip_deliver_contact_360_webhook = false
        allow(contact).to receive(:deliver_contact_360_webhook)
        contact.fill_contact_360_related_data
      end

      it 'calls deliver_contact_360_webhook' do
        expect(contact).to have_received(:deliver_contact_360_webhook)
      end
    end
  end
end
