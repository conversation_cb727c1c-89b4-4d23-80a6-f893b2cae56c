# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Models::CustomView, type: :model do
  subject { described_class }
  let(:organization) { create :organization }
  let(:custom_view) { build_stubbed :custom_view, organization: organization }

  describe 'custom view' do
    it 'return record' do
      expect(custom_view.organization).to eq(organization)
      expect(custom_view.user).to be_instance_of(Models::User)
    end
  end
end
