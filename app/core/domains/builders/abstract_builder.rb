# frozen_string_literal: true

class Builders::AbstractBuilder < CleanArchitecture::Builders::AbstractActiveRecordEntityBuilder
  implements_interface Services::Elasticsearch::Queries::Find
  implements_interface Services::Elasticsearch::Queries::FindBy
  implements_interface Services::Elasticsearch::Queries::Where
  implements_interface Services::Elasticsearch::Queries::Exists

  def self.build(*args)
    new(*args).build
  end

  FeatureFlag = Struct.new(:feature, :for_all?, :info?, :field, :value, :organization_ids, keyword_init: true) do
    def enabled?(organization_id: nil, except_organization_id: nil)
      Services::Preference.new.enabled?(feature, organization_id: organization_id, except_organization_id: except_organization_id, field: field, organization_ids: organization_ids)
    end

    def info
      Services::Preference.new.info(feature, field: field, value: value, organization_ids: organization_ids)
    end
  end
  private_constant :FeatureFlag

  def mask_extra extra
    extra = Hashie::Mash.new(extra)
    extra.update(email: extra.email.to_sensor) unless extra.email.nil?
    extra.update(username: extra.username.to_sensor) unless extra.username.nil?
    extra
  end

  def output_channel_mapper channel
    return 'wa' if channel.eql?('wa_cloud')
    channel
  end

  def channel_is_tokopedia_chat?
    return true if ar_model_instance.channel_integration&.dig(:target_channel)&.eql?('tokopedia_chat')

    ar_model_instance.channel_integration.target_channel.eql?('tokopedia_chat') rescue false
  end

  def shopee_channel?
    ar_model_instance.channel_integration&.dig(:target_channel).eql?('shopee') rescue false
  end

  def wa_channel?
    ar_model_instance.channel_integration&.dig(:target_channel).eql?('wa_cloud') rescue false
  end

  def enable_last_message
    @enable_last_message ||= !Services::Preference.new.enabled?(:disable_last_message)
  end

  # TODO: refactor this method, since the method size is too big
  def prepare_response_settings
    setting              = Hashie::Mash.new @ar_model_instance.settings.as_json
    webhooks             = Models::Webhook.where(organization_id: @ar_model_instance.id)
    caa                  = []
    agent_responsibility = []
    room_interactions    = []
    message_interactions = []
    waba_interactions    = []
    available_user_status = []
    webhooks.each do |webhook|
      case webhook.event
      when 'custom_agent_allocation'
        caa << Builders::Webhook.new(webhook).build.to_hash
      when 'agent_responsibility'
        agent_responsibility << Builders::Webhook.new(webhook).build.to_hash
      when 'room_interaction'
        room_interactions << Builders::Webhooks::RoomInteractions::Main.new(webhook).build.to_hash
      when 'message_interaction'
        message_interactions << Builders::Webhook.new(webhook).build.to_hash
      when 'waba_interaction'
        waba_interactions << Builders::Webhook.new(webhook).build.to_hash
      when 'available_user_status'
        available_user_status << Builders::Webhook.new(webhook).build.to_hash
      end
    end

    preloaded_flags = preload_flags_in_redis

    {
      app_type:                                    setting.app_type || 'full',
      auto_assign_agent:                           setting.auto_assign_agent || false,
      agent_can_takeover:                          setting.agent_can_takeover || false,
      is_override_contact_by_upload:               setting.is_override_contact_by_upload || false,
      agent_can_send_broadcast:                    setting.agent_can_send_broadcast || false,
      agent_can_assign_agent:                      setting.agent_can_assign_agent || false,
      bot_can_settings_webhook:                    setting.bot_can_settings_webhook || false,
      round_robin_all_agent:                       setting.round_robin_all_agent || false,
      round_robin_online_agent:                    setting.round_robin_online_agent || false,
      is_queue_round_robin:                        setting.is_queue_round_robin || false,
      is_contact_masking:                          setting.is_contact_masking || false,
      segment:                                     setting.segment || false,
      include_prev_message_broadcast:              setting.include_prev_message_broadcast || false,
      segment_value:                               setting.segment_value || [],
      broadcast_interval:                          setting.broadcast_interval || 300,
      create_message_template_otp:                 setting.create_message_template_otp || false,
      internal_cluster:                            setting.internal_cluster || false,
      internal_cluster_redirect:                   setting.internal_cluster_redirect || '',
      customer_insights:                           setting.customer_insights || false,
      centralized_refresh_token:                   setting.centralized_refresh_token || false,
      ig_comment:                                  preloaded_flags[:ig_comment].enabled?,
      wa_cloud:                                    preloaded_flags[:wa_cloud].enabled?,
      download_chat_history:                       preloaded_flags[:download_chat_history].enabled?,
      download_chat_history_replica:               preloaded_flags[:download_chat_history_replica].enabled?,
      qontak_channel_ticket:                       preloaded_flags[:qontak_channel_ticket].enabled?(organization_id: @ar_model_instance.id),
      is_mekari_smart_assist_active:               preloaded_flags[:is_mekari_smart_assist_active].enabled?(organization_id: @ar_model_instance.id) || preloaded_flags["is_mekari_smart_assist_active_#{@ar_model_instance.id}"].enabled?,
      broadcast_metabase:                          preloaded_flags[:broadcast_metabase].enabled?,
      upload_contact_list_improvement:             preloaded_flags[:upload_contact_list_improvement].enabled?,
      direct_select_contacts:                      preloaded_flags[:direct_select_contacts].enabled?,
      app_chat:                                    preloaded_flags[:app_chat].enabled?,
      use_new_auto_resolve:                        preloaded_flags[:auto_resolve_v2].enabled?,
      auto_responder_in_office_hours:              {
        enabled: setting.auto_responder_in_office_hours || false,
        message: @ar_model_instance.response_message_in_office_hour || ''
      },
      auto_responder_out_office_hours:             {
        enabled: setting.auto_responder_out_office_hours || false,
        message: @ar_model_instance.response_message_out_office_hour || ''
      },
      custom_agent_allocation:                     {
        enabled:  setting.custom_agent_allocation || false,
        webhooks: caa
      },
      agent_assigned_to_room:                      {
        enabled:  setting.agent_assigned_to_room || false,
        webhooks: agent_responsibility
      },
      agent_unassigned_from_room:                  {
        enabled:  setting.agent_unassigned_from_room || false,
        webhooks: agent_responsibility
      },
      agent_handover_room:                         {
        enabled:  setting.agent_handover_room || false,
        webhooks: agent_responsibility
      },
      room_resolved:                               {
        enabled:  setting.room_resolved || false,
        webhooks: room_interactions
      },
      room_created:                                {
        enabled:  setting.room_created || false,
        webhooks: room_interactions
      },
      receive_message_from_agent:                  {
        enabled:  setting.receive_message_from_agent || false,
        webhooks: message_interactions
      },
      broadcast_log_status:                        {
        enabled:  setting.broadcast_log_status || false,
        webhooks: message_interactions
      },
      receive_message_from_customer:               {
        enabled:  setting.receive_message_from_customer || false,
        webhooks: message_interactions
      },
      status_message:                              {
        enabled:  setting.status_message || false,
        webhooks: message_interactions
      },
      status_template:                             {
        enabled:  setting.status_template || false,
        webhooks: waba_interactions
      },
      available_user_status:                       {
        enabled:  setting.available_user_status || false,
        webhooks: available_user_status
      },
      limit_ongoing_chat:                          {
        enabled: setting.limit_ongoing_chat || setting.limit_ongoing_chat_agent.present? || false,
        type:    (if setting.limit_ongoing_chat_agent.present?
                    'specific_agents'
                  elsif setting.limit_ongoing_chat_division.present?
                    'specific_divisions'
                  else
                    'all_agents'
                  end),
        max:     @ar_model_instance.max_ongoing_chat || 5
      },
      broadcast_rate:                              {
        broadcast_direct_limit:    setting.broadcast_direct_limit || 100,
        broadcast_direct_interval: setting.broadcast_direct_interval || 60,
        broadcast_list_limit:      setting.broadcast_list_limit || 1,
        broadcast_list_interval:   setting.broadcast_list_interval || 300
      },
      room_load_more:                              preloaded_flags[:room_load_more_all].enabled? || preloaded_flags[:room_load_more].enabled?(organization_id: @ar_model_instance.id),
      contact_list_upload:                         (setting.contact_list_upload || preloaded_flags[:contact_list_upload].info&.dig(:value, 'limit') || 5000).to_i,
      integration_with_tokopedia_chat_enabled:     setting.integration_with_tokopedia_chat_enabled,
      integration_with_shopee_chat_enabled:        setting.integration_with_shopee_chat_enabled,
      broadcast_log_histories:                     preloaded_flags[:campaign_without_room].enabled?,
      mqtt_notification:                           setting.mqtt_notification || false,
      tokopedia_onboarding_flow:                   preloaded_flags[:tokopedia_self_onboarding_disabled].enabled?(organization_id: @ar_model_instance.id) ? 'qontak_onboarding' : 'self_onboarding',
      contact_list_upload_csv:                     (setting.contact_list_upload_csv || preloaded_flags[:contact_list_upload_csv].info&.dig(:value, 'limit') || 5000).to_i,
      contact_list_upload_excel:                   (setting.contact_list_upload_excel || preloaded_flags[:contact_list_upload_excel].info&.dig(:value, 'limit') || 5000).to_i,
      new_fb_pricing:                              preloaded_flags[:new_fb_pricing].enabled?,
      dd_histogram:                                preloaded_flags[:dd_histogram].enabled?,
      dd_histogram_sample_rate:                    (preloaded_flags[:dd_histogram].info&.dig(:value, 'sample_rate') || 0.2).to_f,
      download_conversation:                       preloaded_flags[:download_conversation].enabled? || preloaded_flags[:download_conversation_custom_org].enabled?(organization_id: @ar_model_instance.id),
      new_room_ordering_logic:                     preloaded_flags[:new_room_ordering_logic].enabled?,
      show_sync_report_button_on_going_chat_agent: preloaded_flags[:show_sync_report_button_on_going_chat_agent].enabled?,
      message_search:                              preloaded_flags[:message_search_config].info.slice(:value),
      message_search_revamp_ui:                    preloaded_flags[:message_search_revamp_ui].enabled?,
      request_old_chat_history:                    preloaded_flags[:request_old_chat_history].info.slice(:value),
      new_sidebar_ui:                              preloaded_flags[:new_sidebar_ui].enabled?(organization_id: @ar_model_instance.id),
      template_pacing:                             preloaded_flags[:template_pacing].enabled?,
      company_validation:                          preloaded_flags[:company_validation].enabled?,
      is_merge_unmerge_contact_disabled:           is_merge_unmerge_contact_disabled_flag(preloaded_flags[:is_merge_unmerge_contact_disabled].enabled?(organization_id: @ar_model_instance.id)),
      new_survey_flow:                             preloaded_flags[:new_survey_flow].enabled?,
      bulk_update_contact:                         preloaded_flags[:bulk_update_contact].enabled?(organization_id: @ar_model_instance.id),
      conversation_sentiment:                      preloaded_flags[:conversation_sentiment].enabled? || preloaded_flags[:conversation_sentiment_custom_org].enabled?(organization_id: @ar_model_instance.id),
      agent_idle_rule_assignment:                  preloaded_flags[:agent_idle_rule_assignment].enabled?,
      chatbot_tone_of_voice:                       preloaded_flags[:chatbot_tone_of_voice].enabled?,
      spv_filter_division:                         preloaded_flags[:spv_filter_division].enabled?(organization_id: @ar_model_instance.id),
      chatbot_score_card:                          preloaded_flags[:billing_move_flag_to_organization].enabled? ? (@ar_model_instance.chatbot_score_card_custom_org || false) : (preloaded_flags[:chatbot_score_card].enabled? || preloaded_flags[:chatbot_score_card_custom_org].enabled?(organization_id: @ar_model_instance.id)),
      new_dashboard_report:                        preloaded_flags[:new_dashboard_report].enabled? || preloaded_flags[:new_dashboard_report_custom_org].enabled?(organization_id: @ar_model_instance.id),
      reporting_division_filter:                   preloaded_flags[:reporting_division_filter].enabled?,
      delete_contact:                              @ar_model_instance.is_centralized_contact,
      chatbot_conversation_summary:                preloaded_flags[:billing_move_flag_to_organization].enabled? ? (@ar_model_instance.chatbot_score_card_custom_org || false) : preloaded_flags[:chatbot_conversation_summary].enabled?(organization_id: @ar_model_instance.id),
      chatbot:                                     {
        chatbot_room_summary:      preloaded_flags["chatbot_room_summary_#{@ar_model_instance.id}"].enabled?,
        chatbot_auto_room_summary: preloaded_flags["chatbot_auto_room_summary_#{@ar_model_instance.id}"].enabled?,
        chatbot_scorecard:         preloaded_flags["chatbot_scorecard_#{@ar_model_instance.id}"].enabled?,
        chatbot_auto_scorecard:    preloaded_flags["chatbot_auto_scorecard_#{@ar_model_instance.id}"].enabled?,
        chatbot_paraphrase:        preloaded_flags["chatbot_paraphrase_#{@ar_model_instance.id}"].enabled?
      },
      chatbot_automation:                          {
        agent_idle: setting.chatbot_automation.present? ? (setting.chatbot_automation.agent_idle || false) : false
      },
      login_for_conversion_api:                    preloaded_flags[:login_for_conversion_api].enabled?,
      disable_conv_log_paginate_with_search_after: preloaded_flags[:disable_conv_log_paginate_with_search_after].enabled?,
      broadcast_log_max_filter:                    preloaded_flags[:broadcast_log_max_filter].enabled?,
      broadcast_log_max_filter_config:             preloaded_flags[:broadcast_log_max_filter].info.slice(:value),
      enable_inbox_sla:                            preloaded_flags[:control_enable_inbox_sla].enabled? ? (preloaded_flags[:enable_inbox_sla].enabled? || preloaded_flags[:enable_inbox_sla_organization].enabled?(organization_id: @ar_model_instance.id) || preloaded_flags["enable_inbox_sla_#{@ar_model_instance.id}"].enabled?) : false,
      tickets_by_contact_active:                   preloaded_flags[:tickets_by_contact_active].enabled?(organization_id: @ar_model_instance.id),
      inbox_pooling:                               inbox_pooling_settings(setting.inbox_pooling),
      google_my_business:                          preloaded_flags[:google_my_business].enabled?(organization_id: @ar_model_instance.id),
      message_notification_ack:                    preloaded_flags[:message_notification_ack].enabled?(organization_id: @ar_model_instance.id),
      qontak_channel_v3:                           preloaded_flags[:billing_move_flag_to_organization].enabled? ? (@ar_model_instance.qontak_channel_v3 || false) : preloaded_flags[:qontak_channel_v3].enabled?(organization_id: @ar_model_instance.id),
      wa_coexistence:                              setting.wa_coexistence || false,
      bulk_update_contacts:                        preloaded_flags[:bulk_update_contacts].enabled?,
      is_wa_group_active:                          preloaded_flags[:is_wa_group_active].enabled?(organization_id: @ar_model_instance.id),
      consent_owner:                               preloaded_flags[:consent_owner].enabled?,
      security_settings:                           preloaded_flags[:security_settings].enabled?,
      email_broadcast:                             preloaded_flags[:email_broadcast].enabled?(organization_id: @ar_model_instance.id),
      enable_wa_call:                              setting.wa_call.present? ? (setting.wa_call.enabled || false) : false,
      wa_call:                                     {
        enabled:   setting.wa_call.present? ? (setting.wa_call.enabled || false) : false,
        recording: setting.wa_call.present? ? (setting.wa_call.recording || false) : false
      },
      enable_get_division_chat:                    preloaded_flags[:enable_get_division_chat].enabled?(organization_id: @ar_model_instance.id),
      enable_campaign_plan:                        setting.enable_campaign_plan || false,
      campaign_plan_max_frequencies:               (preloaded_flags[:enable_campaign_plan].info&.dig(:value, 'max_frequencies') || 30).to_i,
      campaign_plan_refetch_interval:              (preloaded_flags[:enable_campaign_plan].info&.dig(:value, 'refetch_interval') || 300).to_i,
      broadcast_use_hologres:                      preloaded_flags[:broadcast_use_hologres].enabled?(organization_id: @ar_model_instance.id),
      call_activity_log:                           preloaded_flags[:call_activity_log].enabled?(organization_id: @ar_model_instance.id),
      livechat_channel_integration:                preloaded_flags[:livechat_channel_integration].enabled?(organization_id: @ar_model_instance.id),
      enable_wa_flow:                              preloaded_flags[:enable_wa_flow_global].enabled? || (setting.enable_wa_flow_specific || false),
      enable_export_broadcast_log:                 preloaded_flags[:enable_export_broadcast_log].enabled?,
      revamp_inbox:                                setting.revamp_inbox || false,
      hide_qontak_logo_in_widget:                  preloaded_flags[:hide_qontak_logo_in_widget].enabled?(organization_id: @ar_model_instance.id),
      wa_call_use_voice_service:                   preloaded_flags[:wa_call_use_voice_service].enabled?,
      voice_billing_enabled:                       preloaded_flags[:voice_billing_enabled].enabled?,
      enable_voice_recording:                      preloaded_flags[:enable_voice_recording].enabled?(organization_id: @ar_model_instance.id),
      enable_marketing_message_lite:               preloaded_flags[:enable_marketing_message_lite].enabled?,
      prevent_do_not_auto_resolve_room:            preloaded_flags[:prevent_do_not_auto_resolve_room].enabled?(organization_id: @ar_model_instance.id),
      mqtt_log:                                    get_mqtt_log_settings(preloaded_flags[:mqtt_ack_flush]),
      use_direct_select_360:                       preloaded_flags[:use_direct_select_360].enabled?(organization_id: @ar_model_instance.id),
      is_template_create_async:                    preloaded_flags[:is_template_create_async].enabled?,
      is_enable_custom_view:                       setting.is_enable_custom_view || false
    }
  end

  def get_mqtt_log_settings(mqtt_ack_flush_flag)
    mqtt_log_keys = [
      'mqtt_log_agent_handover_room',
      'mqtt_log_user_online_status',
      'mqtt_log_agent_take_room',
      'mqtt_log_resolve_room',
      'mqtt_log_new_room_created',
      'mqtt_log_room_division_taken',
      'mqtt_log_room_taken',
      'mqtt_log_bot_sent_message',
      'mqtt_log_add_agent',
      'mqtt_log_remove_agent',
      'mqtt_log_system_sent_message',
      'mqtt_log_read_messages',
      'mqtt_log_when_system_send_calls_webhook',
      'mqtt_log_system_auto_assign_room',
      'mqtt_log_send_webhook',
      'mqtt_log_customer_sent_message',
      'mqtt_log_agent_sent_message',
      'mqtt_log_when_system_send_incoming_calls_webhook'
    ]

    mqtt_log_values = REDIS_R.pipelined do |pipeline|
      mqtt_log_keys.each { |key| pipeline.get(key) }
    end

    {
      agent_handover_room:                     mqtt_log_values[0].to_s.eql?('true'),
      user_online_status:                      mqtt_log_values[1].to_s.eql?('true'),
      agent_take_room:                         mqtt_log_values[2].to_s.eql?('true'),
      resolve_room:                            mqtt_log_values[3].to_s.eql?('true'),
      new_room_created:                        mqtt_log_values[4].to_s.eql?('true'),
      room_division_taken:                     mqtt_log_values[5].to_s.eql?('true'),
      room_taken:                              mqtt_log_values[6].to_s.eql?('true'),
      bot_sent_message:                        mqtt_log_values[7].to_s.eql?('true'),
      add_agent:                               mqtt_log_values[8].to_s.eql?('true'),
      remove_agent:                            mqtt_log_values[9].to_s.eql?('true'),
      system_sent_message:                     mqtt_log_values[10].to_s.eql?('true'),
      read_messages:                           mqtt_log_values[11].to_s.eql?('true'),
      when_system_send_calls_webhook:          mqtt_log_values[12].to_s.eql?('true'),
      system_auto_assign_room:                 mqtt_log_values[13].to_s.eql?('true'),
      send_webhook:                            mqtt_log_values[14].to_s.eql?('true'),
      customer_sent_message:                   mqtt_log_values[15].to_s.eql?('true'),
      agent_sent_message:                      mqtt_log_values[16].to_s.eql?('true'),
      when_system_send_incoming_calls_webhook: mqtt_log_values[17].to_s.eql?('true'),
      flush_config:                            {
        interval: (mqtt_ack_flush_flag.info&.dig(:value, 'interval') || 2000).to_i,
        batch:    (mqtt_ack_flush_flag.info&.dig(:value, 'batch') || 10).to_i
      }
    }
  end

  def is_merge_unmerge_contact_disabled_flag(flag)
    return true if flag
    feat = FeatureFlag.new(feature: :check_subscriptions_billing, for_all?: true)
    if feat.enabled?
      @unified_subscription ||= Billings::Services::V2::Redis::Subscriptions::GetUnified.new(@ar_model_instance.company_id).call
      flag = @unified_subscription&.status&.qontak_crm == 'active'
    end
    flag
  rescue
    flag
  end

  def inbox_pooling_settings settings
    default_setting = Hashie::Mash.new(
                        main_is_active: Services::Preference.new.enabled?(:inbox_polling),
                        thresholds:     {
                          unassigned:    0.167,
                          rooms:         0.167,
                          conversations: 0.167,
                          rooms_sla:     0.167
                        },
                        is_active:      {
                          unassigned:    true,
                          rooms:         true,
                          conversations: true,
                          rooms_sla:     true
                        }
    )
    default_setting.merge(settings || {})
  end

  def parse_full_name(name:)
    splitted_name = name.split(/\s+/)

    if splitted_name.count > 1
      first_name = splitted_name[0..-2].join(' ')
      last_name = splitted_name[-1]
    else
      first_name = splitted_name[0]
      last_name = ''
    end

    [first_name, last_name]
  end

  def map_message_broadcast_error(error_messages)
    err = 'n/a'
    return err if error_messages.nil? || error_messages.eql?('{}') || error_messages.eql?('')

    if error_messages.is_a?(Hash)
      if error_messages['billing_error'].present?
        err = error_messages['billing_error'].to_s
      elsif error_messages['description'].present?
        err = error_messages['description'].to_s
      end
    end

    err
  end

  # ADD YOUR FLAG HERE TO PRELOAD IF YOU WANT TO INTRODUCE NEW FLAGGING IN organizations/settings!!
  def preload_flags_in_redis
    feature_flags = [
      FeatureFlag.new(feature: :room_load_more, for_all?: true),
      FeatureFlag.new(feature: :room_load_more_all, for_all?: true),
      FeatureFlag.new(feature: :campaign_without_room, for_all?: true),
      FeatureFlag.new(feature: :ig_comment, for_all?: true),
      FeatureFlag.new(feature: :wa_cloud, for_all?: true),
      FeatureFlag.new(feature: :download_chat_history, for_all?: true),
      FeatureFlag.new(feature: :download_chat_history_replica, for_all?: true),
      FeatureFlag.new(feature: :qontak_channel_ticket, for_all?: false),
      FeatureFlag.new(feature: :is_mekari_smart_assist_active, for_all?: false),
      FeatureFlag.new(feature: "is_mekari_smart_assist_active_#{@ar_model_instance.id}", for_all?: true),
      FeatureFlag.new(feature: :broadcast_metabase, for_all?: true),
      FeatureFlag.new(feature: :upload_contact_list_improvement, for_all?: true),
      FeatureFlag.new(feature: :direct_select_contacts, for_all?: true),
      FeatureFlag.new(feature: :app_chat, for_all?: false),
      FeatureFlag.new(feature: :auto_resolve_v2, for_all?: true),
      FeatureFlag.new(feature: :contact_list_upload, info?: true),
      FeatureFlag.new(feature: :contact_list_upload_csv, info?: true),
      FeatureFlag.new(feature: :contact_list_upload_excel, info?: true),
      FeatureFlag.new(feature: :tokopedia_self_onboarding_disabled, for_all?: false),
      FeatureFlag.new(feature: :new_fb_pricing, for_all?: true),
      FeatureFlag.new(feature: :download_conversation, for_all?: true),
      FeatureFlag.new(feature: :download_conversation_custom_org, for_all?: false),
      FeatureFlag.new(feature: :show_sync_report_button_on_going_chat_agent, for_all?: true),
      FeatureFlag.new(feature: :message_search_config, info?: true),
      FeatureFlag.new(feature: :message_search_revamp_ui, for_all?: true),
      FeatureFlag.new(feature: :request_old_chat_history, info?: true),
      FeatureFlag.new(feature: :new_sidebar_ui, for_all?: false),
      FeatureFlag.new(feature: :template_pacing, for_all?: true),
      FeatureFlag.new(feature: :new_room_ordering_logic, for_all?: true),
      FeatureFlag.new(feature: :dd_histogram, for_all?: true, info?: true),
      FeatureFlag.new(feature: :company_validation, for_all?: true),
      FeatureFlag.new(feature: :new_survey_flow, for_all?: true),
      FeatureFlag.new(feature: :is_merge_unmerge_contact_disabled, for_all?: false),
      FeatureFlag.new(feature: :bulk_update_contact, for_all?: false),
      FeatureFlag.new(feature: :agent_idle_rule_assignment, for_all?: true),
      FeatureFlag.new(feature: :chatbot_tone_of_voice, for_all?: true),
      FeatureFlag.new(feature: :conversation_sentiment, for_all?: true),
      FeatureFlag.new(feature: :conversation_sentiment_custom_org, for_all?: false),
      FeatureFlag.new(feature: :reporting_division_filter, for_all?: true),
      FeatureFlag.new(feature: :spv_filter_division, for_all?: false),
      FeatureFlag.new(feature: :chatbot_score_card, for_all?: true),
      FeatureFlag.new(feature: :chatbot_score_card_custom_org, for_all?: false),
      FeatureFlag.new(feature: :new_dashboard_report, for_all?: true),
      FeatureFlag.new(feature: :new_dashboard_report_custom_org, for_all?: false),
      FeatureFlag.new(feature: :delete_contact, for_all?: false),
      FeatureFlag.new(feature: :chatbot_conversation_summary, for_all?: false),
      FeatureFlag.new(feature: "chatbot_room_summary_#{@ar_model_instance.id}", for_all?: true),
      FeatureFlag.new(feature: "chatbot_auto_room_summary_#{@ar_model_instance.id}", for_all?: true),
      FeatureFlag.new(feature: "chatbot_scorecard_#{@ar_model_instance.id}", for_all?: true),
      FeatureFlag.new(feature: "chatbot_auto_scorecard_#{@ar_model_instance.id}", for_all?: true),
      FeatureFlag.new(feature: "chatbot_paraphrase_#{@ar_model_instance.id}", for_all?: true),
      FeatureFlag.new(feature: :login_for_conversion_api, for_all?: true),
      FeatureFlag.new(feature: :disable_conv_log_paginate_with_search_after, for_all?: true),
      FeatureFlag.new(feature: :broadcast_log_max_filter, for_all?: true, info?: true),
      # SLA CONFIG
      FeatureFlag.new(feature: :enable_inbox_sla, for_all?: true),
      FeatureFlag.new(feature: :enable_inbox_sla_organization, for_all?: false),
      FeatureFlag.new(feature: :control_enable_inbox_sla, for_all?: false),
      FeatureFlag.new(feature: "enable_inbox_sla_#{@ar_model_instance.id}", for_all?: true),
      # END OF SLA CONFIG
      FeatureFlag.new(feature: :tickets_by_contact_active, for_all?: false),
      FeatureFlag.new(feature: :google_my_business, for_all?: false),
      FeatureFlag.new(feature: :qontak_channel_v3, for_all?: false),
      # FeatureFlag.new(feature: :wa_coexistence, for_all?: false),
      FeatureFlag.new(feature: :message_notification_ack, for_all?: false),
      FeatureFlag.new(feature: :bulk_update_contacts, for_all?: true),
      FeatureFlag.new(feature: :is_wa_group_active, for_all?: false),
      FeatureFlag.new(feature: :consent_owner, for_all?: true),
      FeatureFlag.new(feature: :security_settings, for_all?: true),
      FeatureFlag.new(feature: :email_broadcast, for_all?: false),
      FeatureFlag.new(feature: :enable_wa_call, for_all?: false),
      FeatureFlag.new(feature: :enable_get_division_chat, for_all?: false),
      FeatureFlag.new(feature: :enable_campaign_plan, for_all?: true),
      FeatureFlag.new(feature: :broadcast_use_hologres, for_all?: false),
      FeatureFlag.new(feature: :call_activity_log, for_all?: false),
      FeatureFlag.new(feature: :livechat_channel_integration, for_all?: false),
      FeatureFlag.new(feature: :enable_wa_flow_global, for_all?: true),
      FeatureFlag.new(feature: :enable_export_broadcast_log, for_all?: false),
      FeatureFlag.new(feature: :hide_qontak_logo_in_widget, for_all?: false),
      FeatureFlag.new(feature: :wa_call_use_voice_service, for_all?: false),
      FeatureFlag.new(feature: :voice_billing_enabled, for_all?: true),
      FeatureFlag.new(feature: :enable_voice_recording, for_all?: false),
      FeatureFlag.new(feature: :enable_marketing_message_lite, for_all?: false),
      FeatureFlag.new(feature: :prevent_do_not_auto_resolve_room, for_all?: false),
      FeatureFlag.new(feature: :mqtt_ack_flush, for_all?: true, info?: true),
      FeatureFlag.new(feature: :use_direct_select_360, for_all?: false),
      FeatureFlag.new(feature: :billing_move_flag_to_organization, for_all?: true),
      FeatureFlag.new(feature: :is_template_create_async, for_all?: true)
    ]

    preloaded_flags = {}
    batch_size = (feature_flags.size / 20)
    feature_flags.in_groups_of(feature_flags.size / batch_size, false) do |batch_ffs|
      REDIS_W.pipelined do |pipeline|
        batch_ffs.each do |ff|
          feature = ff.feature
          preloaded_flags[feature] = {}
          preloaded_flags[feature][:struct] = ff
          preloaded_flags[feature][:field] = pipeline.hgetall("#{ff.feature}::field")
          preloaded_flags[feature][:organization_ids] = pipeline.lrange("#{ff.feature}::organization_ids", 0, -1) if ff.for_all?.eql?(true)
          preloaded_flags[feature][:value] = pipeline.hgetall("#{ff.feature}::value") if ff.info?.eql?(true)
        end
      end
    end

    preloaded_flags.keys.each do |key|
      feature_flag = preloaded_flags[key][:struct]
      feature_flag.field = preloaded_flags[key][:field].value
      feature_flag.value = preloaded_flags[key][:value]&.value
      feature_flag.organization_ids = preloaded_flags[key][:organization_ids]&.value
      preloaded_flags[key] = feature_flag
    end

    preloaded_flags
  end
end
