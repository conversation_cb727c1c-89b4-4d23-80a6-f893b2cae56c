# frozen_string_literal: true

class Repositories::Organizations::UpdateAgentIdleRuleAssignment < Repositories::AbstractRepository
  def initialize(id, params:)
    @params     = prepare! params, sanitize: false
    @id         = id
  end

  def call
    organization = Models::Organization.find_by(id: @id)
    return failure 'Organization not found' if organization.nil?

    agent_idle_rule_assignment = { enabled: @params.enabled }
    if @params.enabled.to_s.eql?('true')
      agent_idle_rule_assignment[:period] = @params.idle_period
    end
    agent_idle_rule_assignment[:send_mekari_webhook] = @params.send_mekari_webhook || false

    organization.agent_idle_rule_assignment = agent_idle_rule_assignment
    organization.save if organization.agent_idle_rule_assignment_changed?

    delete_cache(organization.id)

    success Builders::Organization.new(organization).build
  rescue => e
    Rollbar.error(e, class: self.class, method: __method__, args: (method(__method__).parameters.map { |_, ctx_arg| { ctx_arg => binding.local_variable_get(ctx_arg) } } rescue 'err parse args*'))
    failure 'fail to update agent idle rule assignment settings'
  end

  private

  def delete_cache(organization_id)
    Services::Redis::Organizations::Del.new(organization_id).call
  end
end
