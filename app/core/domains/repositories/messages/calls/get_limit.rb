# frozen_string_literal: true

class Repositories::Messages::Calls::GetLimit < Repositories::AbstractRepository
  def initialize(phone_number, organization_id)
    @phone_number = phone_number
    @organization_id = organization_id
  end

  def call
    daily_limit = get_call_limit
    max_calls = 5

    is_limit = daily_limit.to_i == max_calls
    weekly_limit = get_weekly_limit
    return success({ is_limit: true, call_count: 0, call_attempts_remaining: 0, last_permission_call_status: nil, first_time_call_timestamp: nil, next_time_call_timestamp: nil }) unless weekly_limit.present?
    return success({ is_limit: true, call_count: 0, call_attempts_remaining: 0, last_permission_call_status: nil, first_time_call_timestamp: nil, next_time_call_timestamp: nil }) if weekly_limit.to_i >= 2

    message = fetch_message_call_permission_request(@organization_id, @phone_number)
    return success({ is_limit: true, call_count: 0, call_attempts_remaining: 0, last_permission_call_status: nil, first_time_call_timestamp: nil, next_time_call_timestamp: nil }) unless message.present?

    first_time_call_timestamp = get_first_time_call_timestamp
    next_time_call_timestamp = nil

    if first_time_call_timestamp.present?
      next_time_call_timestamp = Time.at(first_time_call_timestamp.to_i).utc + 24.hours
      next_time_call_timestamp = next_time_call_timestamp.to_i.to_s
    end

    payload = {
      is_limit:                    is_limit,
      call_count:                  daily_limit.to_i,
      call_attempts_remaining:     max_calls - daily_limit.to_i,
      last_permission_call_status: message[:text],
      first_time_call_timestamp:   first_time_call_timestamp,
      next_time_call_timestamp:    next_time_call_timestamp
    }
    success payload
  end

  def get_call_limit
    key = "outbound_call_count::#{@phone_number}"
    daily = REDIS_R.get key
    daily
  end

  def get_first_time_call_timestamp
    key = "first_time_outbound_call_timestamp::#{@phone_number}"
    daily = REDIS_R.get key
    daily
  end

  def fetch_message_call_permission_request(organization_id, recipient_id)
    message = Repositories::Rooms::CallPermissionRequest::GetLast.new(organization_id: organization_id, recipient_id: recipient_id).call
    Rails.logger.warn('Message call_permission_request not found') unless message.success?
    return nil unless message.success?
    message.success
  end

  def get_weekly_limit
    contact = Models::Contact.find_by(phone_number: @phone_number, organization_id: @organization_id, channel: 'wa_cloud')
    return nil unless contact
    key = "call_permission_request_weekly_limit_#{contact.id}"
    weekly = REDIS_R.get key
    weekly
  end
end
