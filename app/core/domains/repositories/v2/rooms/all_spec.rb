# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Repositories::V2::Rooms::All, es_refresh_index: true do
  def setup_data
    @model_organization = create :organization
    @model_wa = create :channel_wa, organization_id: @model_organization.id
    @model_fb_messenger = create :fb_channel, organization_id: @model_organization.id
    @model_instagram = create :ig_channel, organization_id: @model_organization.id
    @model_wa_cloud = create :wa_cloud_channel, organization_id: @model_organization.id
    @user = create :supervisor0, :searchable, organization: @model_organization
    @spv2 = create :supervisor0, :searchable, organization: @model_organization
    @spv3 = create :supervisor0, :searchable, organization: @model_organization
    @agent = create :agent0, :searchable, organization: @model_organization

    @division = create :agent_division, organization: @model_organization
    @division.channels = [@model_wa]
    @division.users = [@spv2, @agent]

    @division2 = create :agent_division, organization: @model_organization, name: 'divisi 2'
    @division2.users = [@user]
    @division2.channels = [@model_wa]
  end

  before(:context) do
    Services::Preference.new.add :force_recache_es_index_room_cache, title: 'Recache ES room cache index', extra: {}, target: 'feature'
    Services::Preference.new.enable :force_recache_es_index_room_cache

    Services::Preference.new.add :force_recache_es_index_message, title: 'Recache ES message index', extra: {}, target: 'feature'
    Services::Preference.new.enable :force_recache_es_index_message
  end

  def create_messages(room, i, session_at)
    resource = create :text_message, type: 'text', text: "Lorem ipsum No-#{i}", room: room, created_at: session_at, organization: @model_organization
    Services::Elasticsearch::Rooms::SetLastMessage.new(message: resource).call
    Services::Elasticsearch::Rooms::SetSessionAt.new(message: resource).call
    resource
  end

  def create_rooms(type: 'Models::CustomerServiceRoom', total: 12, with_division: false, with_other_division: false)
    division_id = nil
    division_id = @division.id if with_division

    cur_time = Time.zone.now
    (1..total).map do |i|
      # create 8 rooms:
      case i
      when 1..2
        status = 'assigned' # 2 rooms assigned
      when 3
        status = 'resolved' # 1 rooms resolved
      when 4..7
        status = 'unassigned' # 4 rooms unassigned
      else
        status = 'campaign' # 1 rooms campaign
      end

      case i
      when 1..2
        tags = 'Potato' # 2 rooms
      when 3..4
        tags = 'Boboho' # 2 room
      when 5..6
        tags = %w[Potato Boboho] # 2 room
      else
        tags = [] # 2 room no tag
      end

      case i
      when 1..3
        channel_id = @model_wa.id # 3 wa channeled rooms
      when 4..6
        channel_id = @model_fb_messenger.id # 3 fb channeled rooms
      when 7..9
        channel_id = @model_instagram.id # 3 instagram channeled rooms
      else
        channel_id = @model_wa_cloud.id # wa cloud channel rooms
      end

      case i
      when 1..2
        session_at = cur_time - 4.hours # 2 open rooms (now - 16 jam)
      when 3..4
        session_at = cur_time - 18.hours # 2 expiring rooms antara (now - 24)  (now -16)
      else
        session_at = cur_time - 25.hours # 4 expired rooms
      end

      room = create :room, name: "RoomNo#{i}", status: status, organization_id: @model_organization.id, channel_integration_id: channel_id, division_id: division_id
      if with_other_division
        room_other = create :room, name: "RoomNo#{i}", status: status, organization_id: @model_organization.id, channel_integration_id: channel_id, division_id: @division2.id
        create_messages(room_other, 1, session_at + i.minute)
      end
      room.tag_list.add(tags)
      room.save

      create_messages(room, 1, session_at + i.minute)
      create_messages(room, 2, session_at + i.minute)
      create_messages(room, 3, session_at + i.minute)
      create_messages(room, 4, session_at + i.minute)
      create_messages(room, 5, session_at + i.minute)
    end
  end

  def create_agent_rooms
    room1 = create :room, name: 'AgentRoom1', status: 'assigned', organization_id: @model_organization.id, channel_integration_id: @model_wa.id, division_id: @division.id
    room2 = create :room, name: 'AgentRoom2', status: 'assigned', organization_id: @model_organization.id, channel_integration_id: @model_wa.id
    create :agent_participant, contact_able: @agent, room_id: room1.id
    create :agent_participant, contact_able: @agent, room_id: room2.id

    create_messages(room1, 1, Time.zone.now + 1.minute)
    create_messages(room2, 1, Time.zone.now + 1.minute)

    room1.recache_agent_ids
    room2.recache_agent_ids

    Elasticsearch::RoomRepository.new.refresh_index!
  end

  def list_rooms(params = {})
    described_class.new(params).call
  end

  # use cases
  let(:get_all_rooms) { list_rooms(organization_id: @model_organization.id) }
  let(:search_non_specific) { list_rooms(query: 'Room', organization_id: @model_organization.id) }
  let(:search_specific) { list_rooms(query: 'RoomNo2', organization_id: @model_organization.id) }
  let(:search_non_existent) { list_rooms(query: 'RoomNo50', organization_id: @model_organization.id) }
  let(:filter_by_status_unassigned) { list_rooms(status: 'unassigned', organization_id: @model_organization.id) }
  let(:filter_by_status_campaign) { list_rooms(status: 'campaign', organization_id: @model_organization.id) }
  let(:filter_by_status_assigned) { list_rooms(status: 'assigned', organization_id: @model_organization.id) }
  let(:filter_by_status_resolved) { list_rooms(status: 'resolved', organization_id: @model_organization.id) }
  let(:filter_by_tags_tag_1) { list_rooms(tags: ['Potato'], organization_id: @model_organization.id) }
  let(:filter_by_tags_tag_2) { list_rooms(tags: ['Boboho'], organization_id: @model_organization.id) }
  let(:filter_by_tags_tag_1_AND_2) { list_rooms(tags: %w[Potato Boboho], organization_id: @model_organization.id) }
  let(:filter_by_untagged) { list_rooms(tags: [], untagged: true, organization_id: @model_organization.id) }
  let(:filter_by_channel_wa) { list_rooms(channels: ['wa'], organization_id: @model_organization.id) }
  let(:filter_by_channel_fb_messenger) { list_rooms(channels: ['fb'], organization_id: @model_organization.id) }
  let(:filter_by_channel_instagram) { list_rooms(channels: ['ig'], organization_id: @model_organization.id) }
  let(:filter_by_session_open) { list_rooms(sessions: ['open'], organization_id: @model_organization.id) }
  let(:filter_by_session_expiring) { list_rooms(sessions: ['expiring'], organization_id: @model_organization.id) }
  let(:filter_by_session_expired) { list_rooms(sessions: ['expired'], organization_id: @model_organization.id) }
  let(:filter_by_session_all) { list_rooms(sessions: %w[open expiring expired], organization_id: @model_organization.id) }
  let(:filter_old_room) { list_rooms(organization_id: @model_organization.id, limit: 2, offset: 6) }
  let(:search_uniq_character) { list_rooms(query: '^()"/[]{}~~', organization_id: @model_organization.id) }

  describe 'list rooms general' do
    before(:context) do
      setup_data
      create_rooms
      Elasticsearch::RoomRepository.new.refresh_index!
    end

    after(:context) do
      DatabaseCleaner.clean
      create_index(Elasticsearch::RoomRepository, force: true)
    end

    context 'when fetching all rooms' do
      it 'returns all rooms', :aggregate_failures do
        expect_any_instance_of(Elasticsearch::RoomRepository).to receive(:search).with(anything, { routing: @model_organization.id }).and_call_original
        get_with_unlimited = list_rooms(organization_id: @model_organization.id, limit: 5)
        expect(get_with_unlimited.success.response.count).to eql 5
        room_sort = get_with_unlimited.success.response.map { |msg| msg.last_message_at }
        expect(room_sort[0] >= room_sort[1]).to be true
        expect(room_sort[0] >= room_sort[2]).to be true
        expect(room_sort[1] >= room_sort[2]).to be true
        expect(room_sort[0] >= room_sort[3]).to be true
        expect(room_sort[2] >= room_sort[3]).to be true
        expect(room_sort[0] >= room_sort[4]).to be true
        expect(room_sort[3] >= room_sort[4]).to be true
      end
    end

    context 'when searching' do
      it 'returns the result properly', :aggregate_failures do
        expect(search_non_specific.success.response.count).to eql 12
        expect(search_specific.success.response.count).to eql 1
        expect(search_specific.success.response.last).to include(
                                                           name:            'RoomNo2',
                                                           status:          'assigned',
                                                           channel:         'wa',
                                                           channel_account: 'Qontak Ltd',
                                                           unread_count:    0
                                                         )
        expect(search_specific.success.response.last.class.name).to eql('Entities::RoomList::RoomList')
        expect(search_non_existent.success.response.count).to eql 0
        expect(search_specific.success.pagination).to include(
                                                        limit: 50
                                                      )
        expect(search_non_specific.success.pagination).to include(
                                                            limit: 50
                                                          )
        expect(filter_by_channel_fb_messenger.success.response.last).to include(
                                                                          channel:         'fb',
                                                                          channel_account: 'Development page'
                                                                        )
        expect(filter_by_channel_instagram.success.response.last).to include(
                                                                       channel:         'ig',
                                                                       channel_account: 'qontak test ig'
                                                                     )
        expect(search_uniq_character.success.response.count).to eql 0
      end
    end

    context 'when filtering by status (unassigned, assigned, resolved)' do
      it 'returns the result properly', :aggregate_failures do
        expect(filter_by_status_unassigned.success.response.count).to eql 4
        expect(filter_by_status_assigned.success.response.count).to eql 2
        expect(filter_by_status_resolved.success.response.count).to eql 1
      end
    end

    context 'when filtering by tags' do
      it 'returns the result properly', :aggregate_failures do
        expect(filter_by_tags_tag_1.success.response.count).to eql 4
        expect(filter_by_tags_tag_2.success.response.count).to eql 4
        expect(filter_by_tags_tag_1_AND_2.success.response.count).to eql 6
      end
    end

    context 'when filtering by untagged' do
      it 'returns the result properly', :aggregate_failures do
        expect(filter_by_untagged.success.response.count).to eql 6
      end
    end

    context 'when filtering by channels' do
      it 'returns the result properly', :aggregate_failures do
        expect(filter_by_channel_wa.success.response.count).to eql 6
        expect(filter_by_channel_fb_messenger.success.response.count).to eql 3
        expect(filter_by_channel_instagram.success.response.count).to eql 3
      end
    end

    context 'when filtering by sessions' do
      it 'returns the result properly', :aggregate_failures do
        expect(filter_by_session_open.success.response.count).to eql 2
        expect(filter_by_session_expiring.success.response.count).to eql 2
        expect(filter_by_session_expired.success.response.count).to eql 8
        expect(filter_by_session_all.success.response.count).to eql 12
      end
    end

    context 'when filtering with pagination' do
      it 'returns limit rooms', :aggregate_failures do
        page_1 = list_rooms(organization_id: @model_organization.id, limit: 2, offset: 1)
        expect(page_1.success.response.count).to eql 2
        expect(page_1.success.response.map { |room| room.name }).to eql %w[RoomNo2 RoomNo1]

        page_2 = list_rooms(organization_id: @model_organization.id, limit: 2, offset: 2)
        expect(page_2.success.response.count).to eql 2
        expect(page_2.success.response.map { |room| room.name }).to eql %w[RoomNo4 RoomNo3]

        page_3 = list_rooms(organization_id: @model_organization.id, limit: 2, offset: 3)
        expect(page_3.success.response.count).to eql 2
        expect(page_3.success.response.map { |room| room.name }).to eql %w[RoomNo12 RoomNo11]

        page_4 = list_rooms(organization_id: @model_organization.id, limit: 2, offset: 4)
        expect(page_4.success.response.count).to eql 2
        expect(page_4.success.response.map { |room| room.name }).to eql %w[RoomNo10 RoomNo9]

        page_5 = list_rooms(organization_id: @model_organization.id, limit: 2, offset: 5)
        expect(page_5.success.response.count).to eql 2
        expect(page_5.success.response.map { |room| room.name }).to eql %w[RoomNo8 RoomNo7]

        page_6 = list_rooms(organization_id: @model_organization.id, limit: 2, offset: 6)
        expect(page_6.success.response.count).to eql 2
        expect(page_6.success.response.map { |room| room.name }).to eql %w[RoomNo6 RoomNo5]

        page_7 = list_rooms(organization_id: @model_organization.id, limit: 2, offset: 7)
        expect(page_7.success.response.count).to eql 0
        expect(page_7.success.response.map { |room| room.name }).to eql []
      end
    end

    context 'when there is room older than one year from today' do
      let!(:old_room) { create :room, name: 'RoomNo Old', status: 'unassigned', organization_id: @model_organization.id, channel_integration_id: @model_wa.id, created_at: (Time.zone.now - (1.year + 1.day)) }
      let!(:index_old_room) {
        resource = build_stubbed :text_message, type: 'text', text: 'Lorem ipsum No Old', room: old_room, created_at: old_room.created_at + 1.minute, organization: @model_organization
        Services::Elasticsearch::Rooms::SetLastMessage.new(message: resource).call
        Services::Elasticsearch::Rooms::SetSessionAt.new(message: resource).call
        Services::Preference.new.add :max_room_query_date_range_after_retention, title: 'Message search api date range configs', extra: { default_days_range: 60, max_days_range: 365 }, target: 'system'
      }
      it 'should not be listed in the result' do
        result = filter_old_room.value!

        unless result.response.empty?
          expect(result.response.first.id).to_not eql(old_room.id)
        end
      end
    end

    context 'when sorting by sort_by' do
      let(:sort_by_oldest) { list_rooms(organization_id: @model_organization.id, sort_by: 'oldest') }
      let(:sort_by_newest) { list_rooms(organization_id: @model_organization.id, sort_by: 'newest') }

      it 'sorts rooms by last_activity_at in ascending order when sort_by is oldest', :aggregate_failures do
        result = sort_by_oldest.success.response
        expect(result.count).to be > 0
        timestamps = result.map { |room| room.last_activity_at }
        expect(timestamps).to eq(timestamps.sort) # Ensure ascending order
      end

      it 'sorts rooms by last_activity_at in descending order when sort_by is newest', :aggregate_failures do
        result = sort_by_newest.success.response
        expect(result.count).to be > 0
        timestamps = result.map { |room| room.last_activity_at }
        expect(timestamps).to eq(timestamps.sort.reverse) # Ensure descending order
      end
    end

    context 'when filtering by channel_ids' do
      let(:filter_by_wa_channel_id) { list_rooms(organization_id: @model_organization.id, channel_ids: [@model_wa.id]) }
      let(:filter_by_fb_channel_id) { list_rooms(organization_id: @model_organization.id, channel_ids: [@model_fb_messenger.id]) }
      let(:filter_by_instagram_channel_id) { list_rooms(organization_id: @model_organization.id, channel_ids: [@model_instagram.id]) }
      let(:filter_by_wa_cloud_channel_id) { list_rooms(organization_id: @model_organization.id, channel_ids: [@model_wa_cloud.id]) }
      let(:filter_by_multiple_channel_ids) { list_rooms(organization_id: @model_organization.id, channel_ids: [@model_wa.id, @model_fb_messenger.id]) }
      let(:filter_by_all_channel_ids) { list_rooms(organization_id: @model_organization.id, channel_ids: [@model_wa.id, @model_fb_messenger.id, @model_instagram.id, @model_wa_cloud.id]) }
      let(:filter_by_empty_channel_ids) { list_rooms(organization_id: @model_organization.id, channel_ids: []) }
      let(:filter_by_nonexistent_channel_id) { list_rooms(organization_id: @model_organization.id, channel_ids: ['nonexistent-channel-id']) }

      it 'returns rooms filtered by WhatsApp channel ID', :aggregate_failures do
        result = filter_by_wa_channel_id.success.response
        expect(result.count).to eq(3)

        result.each do |room|
          expect(room.channel_integration_id).to eq(@model_wa.id)
        end

        room_names = result.map(&:name)
        expect(room_names).to include('RoomNo1', 'RoomNo2', 'RoomNo3')
      end

      it 'returns rooms filtered by Facebook Messenger channel ID', :aggregate_failures do
        result = filter_by_fb_channel_id.success.response
        expect(result.count).to eq(3)

        result.each do |room|
          expect(room.channel_integration_id).to eq(@model_fb_messenger.id)
        end

        room_names = result.map(&:name)
        expect(room_names).to include('RoomNo4', 'RoomNo5', 'RoomNo6')
      end

      it 'returns rooms filtered by Instagram channel ID', :aggregate_failures do
        result = filter_by_instagram_channel_id.success.response
        expect(result.count).to eq(3)

        result.each do |room|
          expect(room.channel_integration_id).to eq(@model_instagram.id)
        end

        room_names = result.map(&:name)
        expect(room_names).to include('RoomNo7', 'RoomNo8', 'RoomNo9')
      end

      it 'returns rooms filtered by WhatsApp Cloud channel ID', :aggregate_failures do
        result = filter_by_wa_cloud_channel_id.success.response
        expect(result.count).to eq(3)

        result.each do |room|
          expect(room.channel_integration_id).to eq(@model_wa_cloud.id)
        end

        room_names = result.map(&:name)
        expect(room_names).to include('RoomNo10', 'RoomNo11', 'RoomNo12')
      end

      it 'returns rooms filtered by multiple channel IDs', :aggregate_failures do
        result = filter_by_multiple_channel_ids.success.response
        expect(result.count).to eq(6)

        channel_ids = result.map(&:channel_integration_id).uniq
        expect(channel_ids).to contain_exactly(@model_wa.id, @model_fb_messenger.id)

        room_names = result.map(&:name)
        expect(room_names).to include('RoomNo1', 'RoomNo2', 'RoomNo3', 'RoomNo4', 'RoomNo5', 'RoomNo6')
      end

      it 'returns all rooms when filtering by all channel IDs', :aggregate_failures do
        result = filter_by_all_channel_ids.success.response
        expect(result.count).to eq(12)

        channel_ids = result.map(&:channel_integration_id).uniq
        expect(channel_ids).to contain_exactly(@model_wa.id, @model_fb_messenger.id, @model_instagram.id, @model_wa_cloud.id)
      end

      it 'returns all rooms when channel_ids is empty', :aggregate_failures do
        result = filter_by_empty_channel_ids.success.response
        expect(result.count).to eq(12)

        channel_ids = result.map(&:channel_integration_id).uniq
        expect(channel_ids).to contain_exactly(@model_wa.id, @model_fb_messenger.id, @model_instagram.id, @model_wa_cloud.id)
      end

      it 'returns no rooms when filtering by nonexistent channel ID', :aggregate_failures do
        result = filter_by_nonexistent_channel_id.success.response
        expect(result.count).to eq(0)
      end

      it 'filters correctly in combination with other filters', :aggregate_failures do
        combined_filter = list_rooms(
                            organization_id: @model_organization.id,
                            channel_ids:     [@model_wa.id],
                            status:          'assigned'
        )

        result = combined_filter.success.response
        expect(result.count).to eq(2)

        result.each do |room|
          expect(room.channel_integration_id).to eq(@model_wa.id)
          expect(room.status).to eq('assigned')
        end
      end

      it 'respects the OR logic for multiple channel IDs', :aggregate_failures do
        result = filter_by_multiple_channel_ids.success.response

        wa_rooms = result.select { |room| room.channel_integration_id == @model_wa.id }
        fb_rooms = result.select { |room| room.channel_integration_id == @model_fb_messenger.id }

        expect(wa_rooms.count).to eq(3)
        expect(fb_rooms.count).to eq(3)
        expect(wa_rooms.count + fb_rooms.count).to eq(result.count)
      end
    end
  end
end
