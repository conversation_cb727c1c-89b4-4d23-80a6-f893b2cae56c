# frozen_string_literal: true

class Repositories::V2::Rooms::All < Repositories::AbstractRepository
  def initialize(query: '*', offset: 1, limit: Pagy::VARS[:items], order_direction: 'desc', order_by: :last_activity_at,
    organization_id: nil, status: nil, sessions: [], tags: [], channels: [], untagged: false, agent_ids: [], division_ids: [],
    is_admin: false, channel_ids: [], cursor: nil, start_date: nil, end_date: nil, time_offsets: 7,
    actor_role: nil, type_access_inbox: nil, cursor_direction: 'before', actor_id: nil, is_unresponded: nil, sort_by: 'newest')
    @params = search_param! query: query, offset: offset, limit: limit, order_direction: order_direction, order_by: order_by, cursor: cursor, cursor_direction: cursor_direction
    @params.from = @params.offset.abs > 0 ? (@params.offset.abs - 1) * @params.limit : @params.offset * @params.limit
    @status = status
    @sessions = sessions
    @agent_ids = agent_ids
    @organization_id = organization_id
    @tags = tags
    @channels = build_channels(channels)
    @untagged = untagged
    @division_ids = division_ids
    @is_admin = is_admin
    @unsession_channel = UNSESSION_CHANNEL
    @channel_ids = channel_ids
    @date_range = prepare_date_range(start_date, end_date, time_offsets)
    @actor_role = actor_role
    @type_access_inbox = type_access_inbox
    @reverse_order = false
    @actor_id = actor_id
    @is_unresponded = is_unresponded
    @sort_by = sort_by
  end

  attr_reader :params

  def call
    return success Hashie::Mash.new(response: [], pagination: (paginate_manual! [], offset: @params.offset, total: 0, limit: @params.limit)) if @params.from >= 10_000 && @params.cursor.nil?

    query   = search
    records = query.results
    hits    = query.raw_response.dig('hits', 'hits')
    last_pointer = hits.present? ? hits.last['sort'].first : 0
    first_pointer = hits.present? ? hits.first['sort'].first : 0

    data = records.map { |room| build_list(room.attributes) }.compact
    if @reverse_order
      data = data.reverse
    end

    success Hashie::Mash.new(response: data, pagination: (paginate_manual! data, offset: @params.offset, total: data.size, limit: @params.limit, last_pointer: last_pointer, first_pointer: first_pointer))
  end

  def build_list attrs
    unless enable_last_message
      last_message = Services::Redis::Rooms::LastMessage.new(room_id: attrs['id'], created_at: attrs['created_at']).get
      if last_message
        attrs[:last_message] = last_message.as_json
        attrs[:last_message_at] = last_message&.try(:created_at)
      end
    end
    Builders::RoomList::Room.new(Models::Model.new(attrs)).build
  rescue => e
    Rollbar.error(e, class: self.class, method: __method__, args: (method(__method__).parameters.map { |_, ctx_arg| { ctx_arg => binding.local_variable_get(ctx_arg) } } rescue 'err parse args*'))
    nil
  end

  def search
    queries = {}
    filters = []
    shoulds = []

    filters << { term: { organization_id: @organization_id } }
    filters << { terms: { "channel": @channels } } if @channels.present?
    filters << { terms: { "status": [@status].flatten } } if @status.present?

    # filter for division
    if @division_ids.present?
      division_filter = [{ terms: { division_id: ['NULL', general_division] } }]
      division_filter << { terms: { division_id: @division_ids } } if @division_ids.present?
      filters << { bool: { should: division_filter } }
    end

    # filter for channel division
    filters << { bool: { should: { terms: { channel_integration_id: @channel_ids } } } } if @channel_ids.present?

    # filter for sessions
    if @sessions.present?
      sessions = {}
      if @sessions.size.eql?(1)
        sessions = { range: { session_at: wa_session.fetch(@sessions[0]) } }
      elsif @sessions.size.eql?(2)
        if @sessions.include?('open') && @sessions.include?('expiring')
          sessions = { range: { session_at: wa_session.fetch('open_expiring') } }
        elsif @sessions.include?('open') && @sessions.include?('expired')
          sessions = { range: { session_at: wa_session.fetch('open_expired') } }
        elsif @sessions.include?('expiring') && @sessions.include?('expired')
          sessions = { range: { session_at: wa_session.fetch('expiring_expired') } }
        end
      end

      unless @sessions.include?('open')
        filters << { bool: { must_not: { terms: { channel: @unsession_channel } } } }
      end

      filters << sessions if sessions.present?
    end

    musts = []
    musts << { terms: { "agent_ids": @agent_ids } } if @agent_ids.present?
    if HAS_KEYWORD.match?(@params.query)
      musts << {
        "query_string": {
          "query":            "#{@params.query}*",
          "fields":           ['name^1.0', 'account_uniq_id^1.0'],
          "analyze_wildcard": false,
          "type":             'best_fields'
        }
      }
      if Services::Preference.new.enabled? :message_search_config
        filters << { range: { created_at: { gte: @date_range[:start_date], lte: @date_range[:end_date] } } }
      end
    else
      max_range = Services::Preference.new.info(:max_room_query_date_range_after_retention)&.dig(:value, 'max_days_range')
      if max_range.present?
        filters << { range: { created_at: { gte: (Time.zone.now.beginning_of_day - max_range.to_i.days) } } }
      else
        filters << { range: { created_at: { gte: (Time.zone.now.beginning_of_day - 1.year) } } }
      end
    end

    # filter for untagged room
    if @untagged.eql?(true)
      filters << { bool: { must_not: { exists: { field: :tags } } } }
    elsif @tags.present?
      shoulds += @tags.map { |tag| { term: { "tags": { value: tag, case_insensitive: true } } } }
      filters << { bool: { should: shoulds } }
    end

    # filter for is_unresponded
    if @is_unresponded.eql?(true) || @is_unresponded.eql?(false)
      filters << { bool: { should: { match: { "is_unresponded": @is_unresponded } } } }
    end

    # filter for last message exists
    if enable_last_message
      filters << { bool: { must: { exists: { field: :last_message } } } }
    end

    # filter for sorting and pagination
    filters << { bool: { must: { exists: { field: :last_activity_at } } } }
    if @sort_by.eql?('oldest')
      queries[:sort] = [{ last_activity_at: { order: 'asc' } }]
    else
      queries[:sort] = [{ last_activity_at: { order: 'desc' } }]
    end

    queries[:size] = @params.limit
    if @params.cursor.present?
      if @params.cursor_direction.eql?('after')
        @reverse_order = true
        if @sort_by.eql?('oldest')
          queries[:sort] = [{ last_activity_at: { order: 'desc' } }]
        else
          queries[:sort] = [{ last_activity_at: { order: 'asc' } }]
        end
      end
      queries[:search_after] = [DateTime.strptime(@params.cursor.to_s, '%Q')]
    else
      # paramns.from only used if params.search_after is nil
      queries[:from] = @params.from
    end

    queries[:query] = { bool: { filter: filters, must: musts } }
    room_repo = Elasticsearch::RoomRepository.new

    room_repo.search(queries, { routing: @organization_id })
  end

  def general_division
    division = Services::Redis::Divisions::GetGeneral.new(@organization_id).call
    division&.id || 'NULL'
  end

  def wa_session
    # open      gt now-16
    # -> jika  jam session lebih besar daripada (jam sekarang -16)
    #
    # expiring  gt now-24    lt now-16
    # -> jika  jam session lebih besar daripada (jam sekarang - 24) dan lebih kecil daripada (jam seakrang - 16)
    #
    # expired   lt now-24
    # -> jika  jam session lebih kecil daripada (jam sekarang - 24)
    #
    # open-expiring      gt now-24
    # -> jika  jam session lebih besar daripada (jam sekarang - 24)
    #
    # open-expired       gt now-16  lt now-24
    # -> jika  jam session lebih besar daripada (jam sekarang - 16) dan lebih kecil daripada (jam sekarang - 24)
    #
    # expiring-expired   lt now-16
    # -> jika  jam session lebih kecil daripada (jam sekarang - 16)

    {
      'open'             => { gte: (Time.zone.now - 16.hours).iso8601(3), format: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'" }.freeze,
      'expiring'         => { gte: (Time.zone.now - 24.hours).iso8601(3), lte: (Time.zone.now - 16.hours).iso8601(3), format: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'" }.freeze,
      'expired'          => { lte: (Time.zone.now - 24.hours).iso8601(3), format: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'" }.freeze,

      'open_expiring'    => { gte: (Time.zone.now - 24.hours).iso8601(3), format: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'" }.freeze,
      'open_expired'     => { gte: (Time.zone.now - 16.hours).iso8601(3), lte: (Time.zone.now - 24.hours).iso8601(3), format: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'" }.freeze,
      'expiring_expired' => { lte: (Time.zone.now - 16.hours).iso8601(3), format: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'" }.freeze
    }.freeze
  end

  def build_channels(channels)
    channels.push(Models::ChannelIntegration.target_channels['wa_cloud']) if channels.include? Models::ChannelIntegration.target_channels['wa']
    channels.push(Models::ChannelIntegration.target_channels['wa']) if channels.include? Models::ChannelIntegration.target_channels['wa_cloud']
    channels.uniq
  end
end
