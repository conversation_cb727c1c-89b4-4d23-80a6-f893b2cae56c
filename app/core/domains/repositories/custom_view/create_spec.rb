# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Repositories::CustomView::Create do
  let(:user) { create :user1 }
  subject(:call) { described_class.new(params).call }
  let(:params) {
    {
      user_id:         user.id,
      organization_id: user.organization_id,
      name:            fake_uuid,
      icon:            fake_uuid,
      filters:         [
        {
          "field":    'division',
          "value":    ['6ca4c7ae-145f-47bd-8925-18d4d03702a7', 'bd3828b2-44cd-4d33-bef4-69f6b70e0a5a'].to_json,
          "operator": 'in'
        },
        {
          "field":    'unresponded',
          "value":    'true',
          "operator": 'is'
        }
      ]
    }
  }

  def multiple_seed_custom_view org, usr, count = 3
    (1..count).each do |idx|
      create :custom_view, user: usr, name: "some_name #{idx}", organization: org
    end
  end

  describe '#call' do
    context 'success' do
      it 'returns contact', :aggregate_failures do
        expect { call }.to_not raise_exception
        expect(Models::CustomView.first.organization).to eq(user.organization)
      end
    end

    context 'failure' do
      context 'exceed quota' do
        before do
          multiple_seed_custom_view user.organization, user
        end

        it 'should failure exceed quota' do
          expect(Models::CustomView.count).to eq(3)
          expect(call).to be_failure
        end
      end
    end
  end
end
