# frozen_string_literal: true

class Repositories::CustomView::Update < Repositories::AbstractRepository
  def initialize(params)
    @params = prepare! params
  end

  def call
    custom_view = Models::CustomView.find_by id: @params.id, organization_id: @params.organization_id, user_id: @params.user_id
    return failure 'Custom view not found' if custom_view.nil?
    return failure error_messages_for custom_view unless custom_view.update(@params.to_h)

    success Builders::CustomView::Detail.new(custom_view.reload).build
  end
end
