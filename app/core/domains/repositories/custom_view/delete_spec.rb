# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Repositories::CustomView::Delete do
  let(:user) { create :user }
  let(:custom_view) { create :custom_view, user: user, organization: user.organization }
  subject(:call) { described_class.new(id: custom_view.id, organization_id: user.organization_id, user_id: user.id).call }

  describe '#call' do
    context 'success' do
      it 'returns contact', :aggregate_failures do
        expect { call }.to_not raise_exception
        expect(Models::CustomView.count).to eq(0)
      end
    end
  end
end
