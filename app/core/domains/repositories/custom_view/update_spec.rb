# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Repositories::CustomView::Update do
  let(:user) { create :user1 }
  let(:custom_view) { create :custom_view, user_id: user.id, organization_id: user.organization_id }
  subject(:call) { described_class.new(params).call }

  context 'success' do
    before do
      custom_view
    end
    let(:params) {
      {
        id:              custom_view.id,
        user_id:         user.id,
        organization_id: custom_view.organization_id,
        name:            'changed name',
        icon:            'some_icon',
        filters:         [
          {
            "field":    'division',
            "value":    ['6ca4c7ae-145f-47bd-8925-18d4d03702a7', 'bd3828b2-44cd-4d33-bef4-69f6b70e0a5a'].to_json,
            "operator": 'in'
          },
          {
            "field":    'unresponded',
            "value":    'true',
            "operator": 'is'
          }
        ]
      }
    }
    it 'returns contact', :aggregate_failures do
      expect { call }.to change { custom_view.reload.name }.from(nil).to('changed name')
    end
  end
end
