# frozen_string_literal: true

RSpec.describe Repositories::CustomView::Find<PERSON>y do
  let!(:user) { create(:user1) }
  let!(:custom_view) {
    create(:custom_view, organization: user.organization, user: user, name: 'Custom view', icon: 'icon', filters: [
             {
               "field":    'division',
               "value":    ['6ca4c7ae-145f-47bd-8925-18d4d03702a7', 'bd3828b2-44cd-4d33-bef4-69f6b70e0a5a'].to_json,
               "operator": 'in'
             },
             {
               "field":    'unresponded',
               "value":    'true',
               "operator": 'is'
             }
           ])
  }

  subject(:call) { described_class.new(params).call }

  describe '#call' do
    context 'when custom view doesnt exist' do
      let(:params) do
        {
          organization_id: user.organization_id,
          user_id:         user.id,
          id:              'non-existent-id'
        }
      end

      it 'returns nil' do
        expect(call).to be_nil
      end
    end

    context 'when custom view exists' do
      let(:params) do
        {
          organization_id: user.organization_id,
          user_id:         user.id,
          id:              custom_view.id
        }
      end

      it 'returns the custom view' do
        result = call
        expect(result).to be_a(Models::CustomView)
        expect(result.name).to eq('Custom view')
        expect(result.icon).to eq('icon')
        expect(result.filters).to eq([
                                       { 'field' => 'division', 'value' => ['6ca4c7ae-145f-47bd-8925-18d4d03702a7', 'bd3828b2-44cd-4d33-bef4-69f6b70e0a5a'].to_json, 'operator' => 'in' },
                                       { 'field' => 'unresponded', 'value' => 'true', 'operator' => 'is' }
                                     ])
      end
    end
  end
end
