# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Repositories::CustomView::List do
  let!(:user) { create :user1 }
  let!(:another_user) { create :user1, organization_id: user.organization_id }
  subject(:call) { described_class.new(params).call }

  def multiple_seed_custom_views org_id, user_id, count: 3
    (1..count).each do |i|
      create :custom_view, organization_id: org_id, user_id: user_id, name: "Custom View #{i}", icon: "icon-#{i}", filters: [
        {
          "field":    'division',
          "value":    ['6ca4c7ae-145f-47bd-8925-18d4d03702a7', 'bd3828b2-44cd-4d33-bef4-69f6b70e0a5a'].to_json,
          "operator": 'in'
        },
        {
          "field":    'unresponded',
          "value":    'true',
          "operator": 'is'
        }
      ]
    end
  end

  describe '#call' do
    context 'when custom views exist for the user' do
      let(:params) do
        {
          organization_id: user.organization_id,
          user_id:         user.id
        }
      end

      before do
        multiple_seed_custom_views(user.organization_id, user.id)
        multiple_seed_custom_views(user.organization_id, another_user.id, count: 2)
      end

      it 'returns a list of custom views' do
        result = call
        expect(result).to be_an(Array)
        expect(result.size).to eq(3)
        expect(result.first).to be_a(Entities::CustomView::Detail)
      end
    end

    context 'when no custom views exist for the user' do
      let(:params) do
        {
          organization_id: user.organization_id,
          user_id:         user.id
        }
      end

      before do
        multiple_seed_custom_views(another_user.organization_id, another_user.id, count: 2)
      end

      it 'returns an empty array' do
        result = call
        expect(result).to be_an(Array)
        expect(result).to be_empty
      end
    end
  end
end
