# frozen_string_literal: true

class Repositories::CustomView::List < Repositories::AbstractRepository
  def initialize(organization_id:, user_id:)
    @organization_id = organization_id
    @user_id = user_id
  end

  def call
    custom_views = Models::CustomView.where(organization_id: @organization_id, user_id: @user_id).order(created_at: :asc)
    custom_views = custom_views.map { |custom_view| Builders::CustomView::Detail.new(custom_view).build }

    custom_views
  end
end
