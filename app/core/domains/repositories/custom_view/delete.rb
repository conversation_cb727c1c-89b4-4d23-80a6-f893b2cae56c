# frozen_string_literal: true

class Repositories::CustomView::Delete < Repositories::AbstractRepository
  def initialize(id:, organization_id:, user_id:)
    @id = id
    @organization_id = organization_id
  end

  def call
    custom_view = Models::CustomView.find_by id: @id, organization_id: @organization_id, user_id: @user_id
    return failure 'Cannot find custom view' if custom_view.nil?
    return failure error_messages_for custom_view unless custom_view.destroy

    success Hashie::Mash.new(status: :success)
  end
end
