# frozen_string_literal: true

class Repositories::CustomView::Create < Repositories::AbstractRepository
  def initialize(params)
    @params = prepare! params
  end

  def call
    return failure 'Your custom view is exceeding limit' if exceed_custom_view_quota

    custom_view = Models::CustomView.new(@params.as_json)
    return failure error_messages_for custom_view unless custom_view.save

    success Builders::CustomView::Detail.new(custom_view).build
  end

  def exceed_custom_view_quota
    Models::CustomView.where(user_id: @params.user_id, organization_id: @params.organization_id).count >= Models::CustomView::CUSTOM_VIEW_QUOTA
  end
end
