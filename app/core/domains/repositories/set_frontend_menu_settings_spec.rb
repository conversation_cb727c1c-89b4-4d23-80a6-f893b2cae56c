# frozen_string_literal: true

require 'rails_helper'
RSpec.describe Repositories::SetFrontendMenuSettings do
  let(:params) { { "preferences": { "web": { "super_admin": { "homeUrl": { "url": 'whatsapp/contacts' }, "menu": { "whatsapp": { "title": 'Broadcast', "slug": 'whatsapp', "status": true, "icon": 'broadcast', "redirect": 'campaigns', "category": 1 }, "login": { "url": 'login' } }, "submenu": { "inbox": { "title": 'Inbox', "slug": 'inbox', "status": true, "icon": 'las la-inbox' }, "credits": { "title": 'Credits', "slug": 'credits', "status": false, "parent": 'finances', "icon": 'chat' } }, "submenutab": { "chat": { "title": 'Chat', "slug": 'export/chat', "status": true, "parent": 'export', "root": 'reports' }, "conversation": { "title": 'Conversation', "slug": 'export/conversation', "status": true, "parent": 'export', "root": 'reports' }, "organization": { "title": 'Organization', "slug": 'accounts_management/organization', "status": true, "parent": 'accounts_management', "root": 'settings' } }, "bottommenu": { "profile_setting": { "title": 'Profile Setting', "slug": 'settings/accounts_management/users', "status": true, "icon": 'profile', "redirect": 'agents' } } }, "admin": { "homeUrl": { "url": 'inbox' }, "menu": { "inbox": { "title": 'Inbox', "slug": 'inbox', "status": true, "icon": 'inbox', "redirect": '', "category": 1 }, "whatsapp": { "title": 'Broadcast', "slug": 'whatsapp', "status": true, "icon": 'broadcast', "redirect": 'contacts', "category": 1 }, "login": { "url": 'login' } }, "submenu": { "inbox": { "title": 'Inbox', "slug": 'inbox', "status": true, "icon": 'las la-inbox' }, "credits": { "title": 'Credits', "slug": 'credits', "status": false, "parent": 'finances', "icon": 'chat' }, "topup": { "title": 'Topup', "slug": 'topup', "status": true, "icon": 'las la-hand-holding-usd', "parent": 'finance' } }, "submenutab": { "chat": { "title": 'Chat', "slug": 'export/chat', "status": true, "parent": 'export', "root": 'reports' }, "conversation": { "title": 'Conversation', "slug": 'export/conversation', "status": true, "parent": 'export', "root": 'reports' }, "conversation_reports": { "title": 'Conversation', "slug": 'conversation_reports', "status": true, "parent": 'general', "icon": 'las la-chart-bar' } }, "bottommenu": { "profile_setting": { "title": 'Profile Setting', "slug": 'settings/accounts_management/users', "status": true, "icon": 'profile', "redirect": 'agents' } } }, "agent": { "homeUrl": { "url": 'inbox' }, "menu": { "inbox": { "title": 'Inbox', "slug": 'inbox', "status": true, "icon": 'inbox', "redirect": '', "category": 1 }, "contacts": { "title": 'Contacts', "slug": 'contacts', "status": true, "icon": 'contact', "redirect": 'contacts', "category": 1 }, "login": { "url": 'login' } }, "submenu": { "inbox": { "title": 'Inbox', "slug": 'inbox', "status": true, "icon": 'las la-inbox' } }, "bottommenu": { "profile_setting": { "title": 'Profile Setting', "slug": 'settings/accounts_management/users', "status": true, "icon": 'profile', "redirect": 'agents' } } }, "supervisor": { "homeUrl": { "url": 'inbox' }, "menu": { "inbox": { "title": 'Inbox', "slug": 'inbox', "status": true, "icon": 'inbox', "redirect": '', "category": 1 }, "whatsapp": { "title": 'Broadcast', "slug": 'whatsapp', "status": true, "icon": 'broadcast', "redirect": 'contacts', "category": 1 }, "login": { "url": 'login' } }, "submenu": { "inbox": { "title": 'Inbox', "slug": 'inbox', "status": true, "icon": 'las la-inbox' }, "credits": { "title": 'Credits', "slug": 'credits', "status": false, "parent": 'finances', "icon": 'chat' }, "topup": { "title": 'Topup', "slug": 'topup', "status": true, "icon": 'las la-hand-holding-usd', "parent": 'finance' } }, "submenutab": { "chat": { "title": 'Chat', "slug": 'export/chat', "status": true, "parent": 'export', "root": 'reports' }, "conversation": { "title": 'Conversation', "slug": 'export/conversation', "status": true, "parent": 'export', "root": 'reports' }, "conversation_reports": { "title": 'Conversation', "slug": 'conversation_reports', "status": true, "parent": 'general', "icon": 'las la-chart-bar' } }, "bottommenu": { "profile_setting": { "title": 'Profile Setting', "slug": 'settings/accounts_management/users', "status": true, "icon": 'profile', "redirect": 'agents' } } }, "owner": { "homeUrl": { "url": 'inbox' }, "menu": { "inbox": { "title": 'Inbox', "slug": 'inbox', "status": true, "icon": 'inbox', "redirect": '', "category": 1 }, "contacts": { "title": 'Contacts', "slug": 'contacts', "status": true, "icon": 'contact', "redirect": 'contacts', "category": 1 }, "login": { "url": 'login' } }, "submenu": { "inbox": { "title": 'Inbox', "slug": 'inbox', "status": true, "icon": 'las la-inbox' } }, "bottommenu": { "profile_setting": { "title": 'Profile Setting', "slug": 'settings/accounts_management/users', "status": true, "icon": 'profile', "redirect": 'agents' } } }, "member": { "homeUrl": { "url": 'inbox' }, "menu": { "inbox": { "title": 'Inbox', "slug": 'inbox', "status": true, "icon": 'inbox', "redirect": '', "category": 1 }, "contacts": { "title": 'Contacts', "slug": 'contacts', "status": true, "icon": 'contact', "redirect": 'contacts', "category": 1 }, "login": { "url": 'login' } }, "submenu": { "inbox": { "title": 'Inbox', "slug": 'inbox', "status": true, "icon": 'las la-inbox' } }, "bottommenu": { "profile_setting": { "title": 'Profile Setting', "slug": 'settings/accounts_management/users', "status": true, "icon": 'profile', "redirect": 'agents' } } } } } } }
  let(:call) { described_class.new(params: params).call }

  describe '#call' do
    before do
      expect(Repositories::GetFrontendMenuSettings.new('admin').call.success).to eq Hashie::Mash.new
      call
    end

    it 'saved 4 keys in redis' do
      expect(Repositories::GetFrontendMenuSettings.new('super_admin').call.success).not_to eq Hashie::Mash.new
      expect(Repositories::GetFrontendMenuSettings.new('admin').call.success).not_to eq Hashie::Mash.new
      expect(Repositories::GetFrontendMenuSettings.new('supervisor').call.success).not_to eq Hashie::Mash.new
      expect(Repositories::GetFrontendMenuSettings.new('agent').call.success).not_to eq Hashie::Mash.new
      expect(Repositories::GetFrontendMenuSettings.new('owner').call.success).not_to eq Hashie::Mash.new
      expect(Repositories::GetFrontendMenuSettings.new('member').call.success).not_to eq Hashie::Mash.new
    end
  end
end
