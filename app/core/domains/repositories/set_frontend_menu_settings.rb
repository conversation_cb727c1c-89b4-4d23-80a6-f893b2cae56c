# frozen_string_literal: true

class Repositories::SetFrontendMenuSettings < Repositories::AbstractRepository
  include Dry::Monads::Do.for(:call)

  def initialize(params:)
    @params = prepare! params, sanitize: false
  end

  def call
    super_admin_menu = @params.preferences.web.super_admin
    admin_menu = @params.preferences.web.admin
    agent_menu = @params.preferences.web.agent
    supervisor_menu = @params.preferences.web.supervisor
    owner_menu = @params.preferences.web.owner
    member_menu = @params.preferences.web.member

    begin
      Services::Redis::FrontendMenuSettings::Set.new('super_admin', super_admin_menu.to_json).call
      Services::Redis::FrontendMenuSettings::Set.new('admin', admin_menu.to_json).call
      Services::Redis::FrontendMenuSettings::Set.new('agent', agent_menu.to_json).call
      Services::Redis::FrontendMenuSettings::Set.new('supervisor', supervisor_menu.to_json).call
      Services::Redis::FrontendMenuSettings::Set.new('owner', owner_menu.to_json).call
      Services::Redis::FrontendMenuSettings::Set.new('member', member_menu.to_json).call
    rescue => e
      return failure e
    end

    success true
  end
end
