# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Repositories::ChannelIntegrations::AllActiveRecord, type: :repository do
  let(:organization) { create :organization }
  let!(:wa_channel) { create :wa_channel, organization_id: organization.id }
  let!(:wa_cloud_channel) { create :wa_cloud_channel, organization_id: organization.id }
  let!(:ig_channel) { create :ig_channel, organization_id: organization.id }
  let!(:web_chat_channel) { create :channel_web_chat, organization_id: organization.id }
  let!(:deleted_channel) { create :ig_channel, organization_id: organization.id }
  let!(:widget) { create :widget, organization_id: organization.id, channel_integration_id: web_chat_channel.id }

  before do
    deleted_channel.destroy
  end

  describe '#call' do
    context 'when no filters are applied' do
      it 'returns all active channel integrations' do
        repository = described_class.new(query: '*', organization_id: organization.id)

        result = repository.call
        data = result.success.response

        expect(data.size).to eq(5) # when create organization, it will create channel_integration
        expect(data.map(&:id)).to include(wa_channel.id, wa_cloud_channel.id, ig_channel.id, web_chat_channel.id)
      end
    end

    context 'when limit is 1' do
      it 'returns a successful response with data and pagination' do
        repository = described_class.new(query: '*', limit: 1, organization_id: organization.id)

        result = repository.call
        data = result.success.response
        pagination = result.success.pagination

        expect(data.size).to eq(1)
        expect(pagination[:offset]).to eq(1)
        expect(pagination[:total]).to eq(1)
        expect(pagination[:limit]).to eq(1)
      end
    end

    context 'when filtering by ids' do
      it 'returns only the specified channel integrations' do
        repository = described_class.new(query: '*', ids: [wa_channel.id, ig_channel.id], organization_id: organization.id)

        result = repository.call
        data = result.success.response

        expect(data.size).to eq(2)
        expect(data.map(&:id)).to include(wa_channel.id, ig_channel.id)
      end
    end
  end

  describe '#build_list' do
    let(:attrs) do
      Hashie::Mash.new({
        id:             web_chat_channel.id,
        target_channel: 'web_chat',
        settings:       {},
        created_at:     Time.current,
        organization:   organization.id,
        webhook:        '12345'
      })
    end

    it 'modifies settings for web_chat channel' do
      repository = described_class.new(query: '*', organization_id: organization.id)

      result = repository.send(:build_list, web_chat_channel)

      expect(result.settings['title']).to eq('Test Widget')
      expect(result.settings['domain']).to eq('test.com')
      expect(result.settings['is_active']).to eq(true)
    end
  end

  describe '#count_data' do
    it 'returns the correct count of documents in Elasticsearch' do
      repository = described_class.new(query: '*', organization_id: organization.id)

      result = repository.send(:count_data)

      expect(result > 0).to be_truthy
    end
  end
end
