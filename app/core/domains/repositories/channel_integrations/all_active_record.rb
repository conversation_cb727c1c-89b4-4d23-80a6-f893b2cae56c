# frozen_string_literal: true

class Repositories::ChannelIntegrations::AllActiveRecord < Repositories::AbstractRepository
  def initialize(query: '*', offset: 1, limit: Pagy::VARS[:items], order_direction: 'desc', order_by: :created_at, organization_id: nil, target_channel: nil, all: false, ids: [])
    @all = all
    @ids = ids
    @params = search_param!(query: query, offset: offset, limit: limit, order_direction: order_direction, order_by: order_by)

    @organization_id = organization_id
    @target_channel  = if target_channel == 'wa'
      %w[wa wa_cloud]
    else
      target_channel.present? ? [target_channel] : nil
    end
    @params.offset_ar = (@params.offset.abs > 0 ? @params.offset.abs - 1 : 0).abs * @params.limit
  end

  def call
    records = fetch_records
    data = records.map { |record| build_list(record) }.compact
    total = @target_channel.present? ? count_data : data.size
    success Hashie::Mash.new(response: data, pagination: { offset: @params.offset, total: total, limit: @params.limit })
  end

  private

  # ActiveRecord-based query
  def build_query_scope
    query_scope = Models::ChannelIntegration.where.not(target_channel: 'unknown')
    query_scope = query_scope.where(organization_id: @organization_id) if @organization_id
    query_scope = query_scope.where(target_channel: @target_channel) if @target_channel.present?
    query_scope = query_scope.where(id: @ids) if @ids.present?

    if @params.query.present? && @params.query != '*'
      query_scope = query_scope.where('id LIKE ?', "#{@params.query}%")
    end

    query_scope
  end

  # Fetch records based on the built query scope
  def fetch_records
    build_query_scope.order(@params.order_by => @params.order_direction)
      .offset(@params.offset_ar)
      .limit(@params.limit)
  end

  # Count the total number of records
  def count_data
    build_query_scope.count
  end

  # Builds the list of records
  def build_list(record)
    if record.target_channel.eql?('web_chat')
      widget = Models::Widget.find_by(channel_integration_id: record.id)
      if widget.present?
        record.settings = widget.settings.as_json
        record.settings['title'] = widget.title
        record.settings['domain'] = widget.domain
        record.settings['is_active'] = widget.is_active
      end
    end

    if record.target_channel.eql?('ig') && record.settings['instagram_name'].nil?
      record.settings['instagram_name'] = "Instagram_#{record.created_at.to_time.to_i}"
    end

    Builders::ChannelIntegration.new(record).build
  end
end
