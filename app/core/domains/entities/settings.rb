# frozen_string_literal: true

class Entities::Settings < Entities::AbstractEntity
  attribute :auto_assign_agent, Types::Strict::Bool
  attribute :app_type, Types::Strict::String.optional
  attribute :auto_responder_in_office_hours, Types::Strict::Hash.optional
  attribute :auto_responder_out_office_hours, Types::Strict::Hash.optional
  attribute :agent_can_takeover, Types::Strict::Bool
  attribute :is_override_contact_by_upload, Types::Strict::Bool
  attribute :agent_can_assign_agent, Types::Strict::Bool
  attribute :custom_agent_allocation, Types::Strict::Hash.optional
  attribute :agent_assigned_to_room, Types::Strict::Hash.optional
  attribute :agent_unassigned_from_room, Types::Strict::Hash.optional
  attribute :agent_handover_room, Types::Strict::Hash.optional
  attribute :room_resolved, Types::Strict::Hash.optional
  attribute :room_created, Types::Strict::Hash.optional
  attribute :receive_message_from_agent, Types::Strict::Hash.optional
  attribute :broadcast_log_status, Types::Strict::Hash.optional
  attribute :receive_message_from_customer, Types::Strict::Hash.optional
  attribute :status_message, Types::Strict::Hash.optional
  attribute :agent_can_send_broadcast, Types::Strict::Bool
  attribute :status_template, Types::Strict::Hash.optional
  attribute :available_user_status, Types::Strict::Hash.optional
  attribute :round_robin_all_agent, Types::Strict::Bool
  attribute :round_robin_online_agent, Types::Strict::Bool
  attribute :limit_ongoing_chat, Types::Strict::Hash.optional
  attribute :is_queue_round_robin, Types::Strict::Bool
  attribute :is_contact_masking, Types::Strict::Bool
  attribute :broadcast_interval, Types::Integer.optional
  attribute :ig_comment, Types::Strict::Bool
  attribute :wa_cloud, Types::Strict::Bool
  attribute :download_chat_history, Types::Strict::Bool
  attribute :download_chat_history_replica, Types::Strict::Bool
  attribute :broadcast_metabase, Types::Strict::Bool
  attribute :upload_contact_list_improvement, Types::Strict::Bool
  attribute :direct_select_contacts, Types::Strict::Bool
  attribute :app_chat, Types::Strict::Bool
  attribute :segment, Types::Strict::Bool.optional
  attribute :include_prev_message_broadcast, Types::Strict::Bool.optional
  attribute :segment_value, Types::Strict::Array.optional
  attribute :contact_list_upload, Types::Integer.optional
  attribute :contact_list_upload_csv, Types::Integer.optional
  attribute :contact_list_upload_excel, Types::Integer.optional
  attribute :broadcast_rate, Types::Hash.optional
  attribute? :custom_rate_limiters, Types::Hash.optional
  attribute :bot_can_settings_webhook, Types::Strict::Bool
  attribute? :integration_with_tokopedia_chat_enabled, Types::Strict::Bool.optional
  attribute? :tokopedia_onboarding_flow, Types::Strict::String.optional
  attribute? :new_fb_pricing, Types::Strict::Bool.optional
  attribute? :room_load_more, Types::Strict::Bool.optional
  attribute? :qontak_channel_ticket, Types::Strict::Bool.optional
  attribute? :dd_histogram, Types::Strict::Bool.optional
  attribute? :dd_histogram_sample_rate, Types::Strict::Float.optional
  attribute? :is_mekari_smart_assist_active, Types::Strict::Bool.optional
  attribute? :download_conversation, Types::Strict::Bool.optional
  attribute? :new_room_ordering_logic, Types::Strict::Bool.optional
  attribute :show_sync_report_button_on_going_chat_agent, Types::Strict::Bool
  attribute? :message_search, Types::Strict::Hash.optional
  attribute? :mqtt_notification, Types::Strict::Bool.optional
  attribute? :message_search_revamp_ui, Types::Strict::Bool.optional
  attribute? :request_old_chat_history, Types::Strict::Hash.optional
  attribute? :new_sidebar_ui, Types::Strict::Bool.optional
  attribute? :template_pacing, Types::Strict::Bool.optional
  attribute? :company_validation, Types::Strict::Bool.optional
  attribute? :is_merge_unmerge_contact_disabled, Types::Strict::Bool.optional
  attribute? :new_survey_flow, Types::Strict::Bool.optional
  attribute? :use_new_auto_resolve, Types::Strict::Bool.optional
  attribute? :bulk_update_contact, Types::Strict::Bool.optional
  attribute? :agent_idle_rule_assignment, Types::Strict::Bool.optional
  attribute? :conversation_sentiment, Types::Strict::Bool.optional
  attribute? :chatbot_tone_of_voice, Types::Strict::Bool.optional
  attribute? :spv_filter_division, Types::Strict::Bool.optional
  attribute? :chatbot_score_card, Types::Strict::Bool.optional
  attribute? :new_dashboard_report, Types::Strict::Bool.optional
  attribute? :integration_with_shopee_chat_enabled, Types::Strict::Bool.optional
  attribute? :broadcast_log_histories, Types::Strict::Bool.optional
  attribute? :reporting_division_filter, Types::Strict::Bool.optional
  attribute? :delete_contact, Types::Strict::Bool.optional
  attribute? :chatbot_conversation_summary, Types::Strict::Bool.optional
  attribute? :chatbot, Types::Strict::Hash.optional
  attribute? :login_for_conversion_api, Types::Strict::Bool.optional
  attribute? :disable_conv_log_paginate_with_search_after, Types::Strict::Bool.optional
  attribute? :broadcast_log_max_filter, Types::Strict::Bool.optional
  attribute? :broadcast_log_max_filter_config, Types::Strict::Hash.optional
  attribute? :enable_inbox_sla, Types::Strict::Bool.optional
  attribute? :tickets_by_contact_active, Types::Strict::Bool.optional
  attribute :inbox_pooling, Types::Hash.optional
  attribute? :message_notification_ack, Types::Strict::Bool.optional
  attribute? :google_my_business, Types::Strict::Bool.optional
  attribute? :qontak_channel_v3, Types::Strict::Bool.optional
  attribute? :wa_coexistence, Types::Strict::Bool.optional
  attribute? :bulk_update_contacts, Types::Strict::Bool.optional
  attribute? :is_wa_group_active, Types::Strict::Bool.optional
  attribute? :create_message_template_otp, Types::Strict::Bool.optional
  attribute? :consent_owner, Types::Strict::Bool.optional
  attribute? :security_settings, Types::Strict::Bool.optional
  attribute? :email_broadcast, Types::Strict::Bool.optional
  attribute? :enable_wa_call, Types::Strict::Bool.optional
  attribute? :wa_call, Types::Strict::Hash.optional
  attribute? :chatbot_automation, Types::Strict::Hash.optional
  attribute? :enable_get_division_chat, Types::Strict::Bool.optional
  attribute? :enable_campaign_plan, Types::Strict::Bool.optional
  attribute? :campaign_plan_max_frequencies, Types::Strict::Integer.optional
  attribute? :campaign_plan_refetch_interval, Types::Strict::Integer.optional
  attribute? :internal_cluster, Types::Strict::Bool.optional
  attribute? :internal_cluster_redirect, Types::Strict::String.optional
  attribute? :call_activity_log, Types::Strict::Bool.optional
  attribute? :livechat_channel_integration, Types::Strict::Bool.optional
  attribute? :enable_wa_flow, Types::Strict::Bool.optional
  attribute? :enable_export_broadcast_log, Types::Strict::Bool.optional
  attribute? :revamp_inbox, Types::Strict::Bool.optional
  attribute? :hide_qontak_logo_in_widget, Types::Strict::Bool.optional
  attribute? :wa_call_use_voice_service, Types::Strict::Bool.optional
  attribute? :enable_voice_recording, Types::Strict::Bool.optional
  attribute? :voice_billing_enabled, Types::Strict::Bool.optional
  attribute? :enable_marketing_message_lite, Types::Strict::Bool.optional
  attribute? :prevent_do_not_auto_resolve_room, Types::Strict::Bool.optional
  attribute? :mqtt_log, Types::Strict::Hash.optional
  attribute? :customer_insights, Types::Strict::Bool.optional
  attribute? :centralized_refresh_token, Types::Strict::Bool.optional
  attribute? :use_direct_select_360, Types::Strict::Bool.optional
  attribute? :is_template_create_async, Types::Strict::Bool.optional
  attribute? :is_enable_custom_view, Types::Strict::Bool.optional
end
