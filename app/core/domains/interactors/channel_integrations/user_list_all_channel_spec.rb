# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Interactors::ChannelIntegrations::UserListAllChannel do
  let(:organization) { create :organization, :searchable }
  let!(:channel) { create :channel_integration, organization_id: organization.id }
  let!(:channel_wa) { create :channel_wa, organization_id: organization.id }
  let!(:channel_ig) { create :channel_ig, organization_id: organization.id }
  let(:params) { described_class.parameters(organization_id: channel.organization_id) }
  let(:params_all) { described_class.parameters(organization_id: channel.organization_id, all: true) }
  let(:params_wa) { described_class.parameters(organization_id: channel_wa.organization_id, target_channel: 'wa') }
  let(:params_ig) { described_class.parameters(organization_id: channel_ig.organization_id, target_channel: 'ig') }

  let(:use_case) { described_class.new(params).result }
  let(:use_case_all) { described_class.new(params_all).result }
  let(:use_case_wa) { described_class.new(params_wa).result }
  let(:use_case_ig) { described_class.new(params_ig).result }

  describe '# when user is online Results list channel' do
    it 'returns paginated channels' do
      expect(use_case.success['pagination']).to include(offset: 1, limit: 50, total: an_instance_of(Integer))
      expect(use_case.success['response']).to include(an_object_having_attributes(
                                                        organization_id: channel.organization_id,
                                                        target_channel:  channel.target_channel
                                                      ))
    end

    it 'returns paginated channels wa and wa_cloud' do
      create :channel, organization_id: organization.id, target_channel: 'wa_cloud'
      expect(use_case_wa.success['pagination']).to include(offset: 1, limit: 50, total: an_instance_of(Integer))
      expect(use_case_wa.success.response.map { |res| res.organization_id }).to include(channel_wa.organization_id)
      expect(use_case_wa.success.response.map { |res| res.target_channel }).to include('wa', 'wa_cloud')
    end

    it 'returns paginated channels ig' do
      expect(use_case_ig.success['pagination']).to include(offset: 1, limit: 50, total: an_instance_of(Integer))
      expect(use_case_ig.success.response.map { |res| res.target_channel }).to include('ig')
      expect(use_case_ig.success.response.map { |res| res.settings['instagram_name'] }).to include('Instagram_1655357871')
    end

    context 'when given params all' do
      it 'returns paginated channels' do
        expect(use_case_all.success['pagination']).to include(offset: 1, limit: 500, total: an_instance_of(Integer))
        expect(use_case_all.success['response']).to include(an_object_having_attributes(
                                                              organization_id: channel.organization_id,
                                                              target_channel:  channel.target_channel
                                                        ))
      end
    end

    context 'when given params all and there is an deleted channel' do
      it 'returns paginated channels' do
        expect(use_case_all.success['pagination']).to include(offset: 1, limit: 500, total: an_instance_of(Integer))
        expect(use_case_all.success['response']).to include(an_object_having_attributes(
                                                              organization_id: channel.organization_id,
                                                              target_channel:  channel.target_channel
                                                        ))
        expect(use_case_all.success['response'].size).to eq(4)
      end
    end

    context 'when assigned_to_me is true but user_id is not provided' do
      let(:params_with_assigned_to_me) { described_class.parameters(organization_id: channel.organization_id, assigned_to_me: true) }

      it 'returns an error' do
        result = described_class.new(params_with_assigned_to_me).result
        expect(result.failure).to eq('User ID is required for assigned_to_me filter')
      end
    end

    context 'when assigned_to_me is true and user is admin' do
      let(:user) { create :user, organization_id: organization.id }
      let(:params_with_assigned_to_me) { described_class.parameters(organization_id: channel.organization_id, assigned_to_me: true, user_id: user.id) }

      it 'returns all channels' do
        result = described_class.new(params_with_assigned_to_me).result
        expect(result.success['response']).to include(an_object_having_attributes(
                                                        organization_id: channel.organization_id,
                                                        target_channel:  channel.target_channel
        ))
        expect(result.success['pagination']).to include(offset: 1, limit: 50, total: 4)
      end
    end

    context 'when assigned_to_me is true and user is supervisor but spv_filter_division is not enabled' do
      let(:user) { create :user, organization_id: organization.id, role: 'supervisor', extra: { 'type_access_inbox' => 'all' } }
      let(:params_with_assigned_to_me) { described_class.parameters(organization_id: channel.organization_id, assigned_to_me: true, user_id: user.id) }

      it 'returns all channels' do
        result = described_class.new(params_with_assigned_to_me).result
        expect(result.success['response']).to include(an_object_having_attributes(
                                                        organization_id: channel.organization_id,
                                                        target_channel:  channel.target_channel
        ))
        expect(result.success['pagination']).to include(offset: 1, limit: 50, total: 4)
      end
    end

    context 'when assigned_to_me is true and user is supervisor but spv_filter_division is not enabled and he assigned the divisions' do
      let(:user) { create :user, organization_id: organization.id, role: 'supervisor', extra: { 'type_access_inbox' => 'all' } }
      let(:params_with_assigned_to_me) { described_class.parameters(organization_id: channel.organization_id, assigned_to_me: true, user_id: user.id) }

      before do
        Services::Redis::Divisions::ResetChannelsByUser.new(user.id, channel_ids: [channel.id]).call
      end

      it 'returns channels assigned to the user' do
        result = described_class.new(params_with_assigned_to_me).result
        expect(result.success['response']).to include(an_object_having_attributes(
                                                        organization_id: channel.organization_id,
                                                        target_channel:  channel.target_channel
        ))
        expect(result.success['pagination']).to include(offset: 1, limit: 50, total: 1)
      end
    end

    context 'when assigned_to_me is true and user is supervisor but type_access_inbox is division_only' do
      let(:user) { create :user, organization_id: organization.id, role: 'supervisor', extra: { 'type_access_inbox' => 'division_only' } }
      let(:params_with_assigned_to_me) { described_class.parameters(organization_id: channel.organization_id, assigned_to_me: true, user_id: user.id) }

      before do
        Services::Redis::Divisions::ResetChannelsByUser.new(user.id, channel_ids: [channel.id]).call
        Services::Preference.new.add :spv_filter_division, title: 'chat access flag', target: 'feature'
        Services::Preference.new.enable :spv_filter_division
        Services::Preference.new.set_organization_ids :spv_filter_division, [organization.id]
      end

      it 'returns channels assigned to the user' do
        result = described_class.new(params_with_assigned_to_me).result
        expect(result.success['response']).to include(an_object_having_attributes(
                                                        organization_id: channel.organization_id,
                                                        target_channel:  channel.target_channel
        ))
        expect(result.success['pagination']).to include(offset: 1, limit: 50, total: 1)
      end
    end

    context 'when assigned_to_me is true and user is supervisor but type_access_inbox is all' do
      let(:user) { create :user, organization_id: organization.id, role: 'supervisor', extra: { 'type_access_inbox' => 'all' } }
      let(:params_with_assigned_to_me) { described_class.parameters(organization_id: channel.organization_id, assigned_to_me: true, user_id: user.id) }

      before do
        Services::Redis::Divisions::ResetChannelsByUser.new(user.id, channel_ids: [channel.id]).call
        Services::Preference.new.add :spv_filter_division, title: 'chat access flag', target: 'feature'
        Services::Preference.new.enable :spv_filter_division
        Services::Preference.new.set_organization_ids :spv_filter_division, [organization.id]
      end

      it 'returns all channels' do
        result = described_class.new(params_with_assigned_to_me).result
        expect(result.success['response']).to include(an_object_having_attributes(
                                                        organization_id: channel.organization_id,
                                                        target_channel:  channel.target_channel
        ))
        expect(result.success['pagination']).to include(offset: 1, limit: 50, total: 4)
      end
    end

    context 'when assigned_to_me is true and user is agent' do
      let(:user) { create :user, organization_id: organization.id, role: 'agent' }
      let(:params_with_assigned_to_me) { described_class.parameters(organization_id: channel.organization_id, assigned_to_me: true, user_id: user.id) }

      before do
        Services::Redis::Divisions::ResetChannelsByUser.new(user.id, channel_ids: [channel.id, channel_ig.id]).call
      end

      it 'returns channels assigned to the user' do
        result = described_class.new(params_with_assigned_to_me).result
        expect(result.success['response']).to include(an_object_having_attributes(
                                                        organization_id: channel.organization_id,
                                                        target_channel:  channel.target_channel
        ))
        expect(result.success['pagination']).to include(offset: 1, limit: 50, total: 2)
      end
    end
  end
end
