# frozen_string_literal: true

class Interactors::ChannelIntegrations::UserListAllChannel < Interactors::AbstractIteractor
  contract do
    params(Interactors::AbstractIteractor.pagination_schema) do
      required(:organization_id).filled(:string)
      optional(:target_channel).maybe(:string, included_in?: Models::ChannelIntegration.known_channels)
      optional(:all).filled(:bool)
      optional(:assigned_to_me).maybe(:bool)
      optional(:user_id).maybe(:string)
    end
  end

  include Dry::Monads[:result]
  include Dry::Monads::Do.for(:result)

  def result
    params = yield result_of_validating_params
    params = yield validate_assigned_to_me(params)

    params[:ids] = get_channel_integration_ids(params) if params[:assigned_to_me]

    Repositories::ChannelIntegrations::AllActiveRecord.new(params.except(:assigned_to_me, :user_id)).call
  end

  private

  def get_channel_integration_ids(params)
    return [] unless params[:assigned_to_me] && params[:user_id].present?
    user = find_user(params[:user_id])
    return [] if user.role.eql?('admin') || user.role.eql?('owner')

    if user.role.eql?('supervisor') && Services::Preference.new.enabled?(:spv_filter_division, organization_id: params[:organization_id])
      return [] if (user.extra&.dig('type_access_inbox') || 'all') == 'all'
    end

    Services::Redis::Divisions::GetChannelsByUser.new(user.id, []).call
  rescue => e
    Rails.logger.error("Failed to get channel integration IDs for user #{params[:user_id]}: #{e.message}")
    []
  end

  def validate_assigned_to_me(params)
    return Success params unless params[:assigned_to_me]
    return Failure 'User ID is required for assigned_to_me filter' if params[:user_id].blank?
    Success params
  end

  def find_user(user_id)
    Services::Redis::Users::Get.new(user_id).call
  end
end
