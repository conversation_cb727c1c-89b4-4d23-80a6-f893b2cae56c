# frozen_string_literal: true

require 'rails_helper'
RSpec.describe Interactors::CustomView::UserDeleteCustomView do
  subject(:results) { described_class.new(described_class.parameters(params)).result }
  let(:params) {
    {
      id:              fake_uuid,
      organization_id: fake_uuid,
      user_id:         fake_uuid
    }
  }
  describe '#Valid attribute' do
    context 'when provided with valid attributes' do
      before do
        stub_repository(Repositories::CustomView::Delete, Hashie::Mash.new(status: 'success'))
      end

      it 'should be success' do
        expect(results).to be_success
      end
    end

    context 'invalid params' do
      it 'should return failure' do
        expect(results).to be_failure
      end
    end
  end
end
