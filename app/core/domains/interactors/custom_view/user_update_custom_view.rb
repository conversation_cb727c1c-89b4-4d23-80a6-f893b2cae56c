# frozen_string_literal: true

class Interactors::CustomView::UserUpdateCustomView < Interactors::AbstractIteractor
  contract do
    params do
      required(:id).filled(:string)
      required(:user_id).filled(:string)
      required(:organization_id).filled(:string)
      required(:name).filled(:string)
      required(:icon).filled(:string)
      required(:filters).filled(:array)
    end
  end

  include Dry::Monads::Do.for(:result)
  include Dry::Monads[:result]

  def result
    params = yield result_of_validating_params
    yield get_organization params[:organization_id]

    Repositories::CustomView::Create.new(params).call
  end

  private

  def get_organization id
    org = Services::Redis::Organizations::Get.new(id).call
    return Failure 'Organization is not found' if org.nil?

    Success org
  end
end
