# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Interactors::CustomView::UserListCustomViews do
  subject(:result) { described_class.new(described_class.parameters(params)).result }
  let(:user) { create(:user1) }

  describe '#result' do
    context 'when some params is missing' do
      let(:params) { {} }

      it 'returns failure' do
        expect(result).to be_failure
      end
    end

    context 'when organization not found' do
      let(:params) { { actor_id: user.id, organization_id: 'non-existent-id' } }

      it 'returns failure' do
        expect(result).to be_failure
        expect(result.failure).to eq('Organization not found')
      end
    end

    context 'when user not found' do
      let(:params) { { actor_id: 'non-existent-user-id', organization_id: user.organization_id } }

      it 'returns failure' do
        expect(result).to be_failure
        expect(result.failure).to eq('User not found')
      end
    end

    context 'when valid params are provided' do
      let(:params) { { actor_id: user.id, organization_id: user.organization_id } }

      before do
        custom_view = create(:custom_view, organization: user.organization, user: user, name: 'Test View', icon: 'test-icon', filters: [{ field: 'status', value: 'active', operator: 'is' }])
        allow(Repositories::CustomView::List).to receive(:call).and_return([Builders::CustomView::Detail.new(custom_view).build])
      end

      it 'returns success with custom views' do
        expect(result).to be_success
        expect(result.success).to be_an(Array)
        expect(result.success.first.name).to eq('Test View')
      end
    end
  end
end
