# frozen_string_literal: true

require 'rails_helper'
RSpec.describe Interactors::CustomView::UserCreateCustomView do
  subject(:results) { described_class.new(described_class.parameters(params)).result }
  let(:organization) { create :organization }

  describe '#Valid attribute' do
    context 'when provided with valid attributes' do
      let(:params) {
        {
          icon:            fake_uuid,
          organization_id: organization.id,
          name:            fake_uuid,
          filters:         [{
            "field":    'division',
            "value":    ['6ca4c7ae-145f-47bd-8925-18d4d03702a7', 'bd3828b2-44cd-4d33-bef4-69f6b70e0a5a'].to_json,
            "operator": 'in'
          }],
          user_id:         fake_uuid
        }
      }

      before do
        stub_repository(Repositories::CustomView::Create, Hashie::Mash.new(status: 'success'))
      end

      it 'should be success' do
        expect(results).to be_success
      end
    end

    context 'when provided with valid attributes' do
      let(:params) {
        {
          icon:            fake_uuid,
          organization_id: fake_uuid,
          name:            fake_uuid,
          filters:         [],
          user_id:         fake_uuid
        }
      }

      it 'should be success' do
        expect(results).to be_failure
      end
    end
  end
end
