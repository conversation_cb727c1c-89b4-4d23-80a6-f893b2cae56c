# frozen_string_literal: true

class Interactors::CustomView::UserListCustomViews < Interactors::AbstractIteractor
  contract do
    params do
      required(:actor_id).filled(:string)
      required(:organization_id).filled(:string)
    end
  end

  include Dry::Monads::Do.for(:result)
  include Dry::Monads[:result]

  def result
    params = yield result_of_validating_params

    organization = Services::Redis::Organizations::Get.new(params[:organization_id]).call
    return Failure('Organization not found') unless organization

    user = Services::Redis::Users::Get.new(params[:actor_id]).call
    return Failure('User not found') unless user

    custom_views = Repositories::CustomView::List.new(organization_id: organization.id, user_id: user.id).call
    Success(custom_views)
  end
end
