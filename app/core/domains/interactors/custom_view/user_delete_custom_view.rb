# frozen_string_literal: true

class Interactors::CustomView::UserDeleteCustomView < Interactors::AbstractIteractor
  contract do
    params do
      required(:id).filled(:string)
      required(:user_id).filled(:string)
      required(:organization_id).filled(:string)
    end
  end

  include Dry::Monads::Do.for(:result)
  include Dry::Monads[:result]

  def result
    params = yield result_of_validating_params
    Repositories::CustomView::Delete.new(id: params[:id], organization_id: params[:organization_id], user_id: params[:user_id]).call
  end
end
