# frozen_string_literal: true

class Interactors::CustomView::UserDetailCustomView < Interactors::AbstractIteractor
  contract do
    params do
      required(:actor_id).filled(:string)
      required(:organization_id).filled(:string)
      required(:id).filled(:string)
    end
  end

  include Dry::Monads::Do.for(:result)
  include Dry::Monads[:result]

  def result
    params = yield result_of_validating_params

    organization = Services::Redis::Organizations::Get.new(params[:organization_id]).call
    return Failure('Organization not found') unless organization

    user = Services::Redis::Users::Get.new(params[:actor_id]).call
    return Failure('User not found') unless user

    custom_view = Repositories::CustomView::FindBy.new(id: params[:id], organization_id: organization.id, user_id: user.id).call
    return Failure('Custom view not found') unless custom_view

    Success(Builders::CustomView::Detail.new(custom_view).build)
  end
end
