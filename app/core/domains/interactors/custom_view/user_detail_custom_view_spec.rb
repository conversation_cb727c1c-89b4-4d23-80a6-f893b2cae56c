# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Interactors::CustomView::UserDetailCustomView do
  let!(:user) { create(:user1) }
  let!(:custom_view) {
    create(:custom_view, organization: user.organization, user: user, name: 'Custom view', icon: 'icon', filters: [
             {
               "field":    'division',
               "value":    ['6ca4c7ae-145f-47bd-8925-18d4d03702a7', 'bd3828b2-44cd-4d33-bef4-69f6b70e0a5a'].to_json,
               "operator": 'in'
             },
             {
               "field":    'unresponded',
               "value":    'true',
               "operator": 'is'
             }
           ])
  }
  subject(:result) { described_class.new(described_class.parameters(params)).result }

  describe '#result' do
    context 'when some params are missing' do
      let(:params) { {} }

      it 'returns failure' do
        expect(result).to be_failure
      end
    end

    context 'when organization not found' do
      let(:params) { { actor_id: user.id, organization_id: 'non-existent-id', id: custom_view.id } }

      it 'returns failure' do
        expect(result).to be_failure
        expect(result.failure).to eq('Organization not found')
      end
    end

    context 'when user not found' do
      let(:params) { { actor_id: 'non-existent-user-id', organization_id: user.organization_id, id: custom_view.id } }

      it 'returns failure' do
        expect(result).to be_failure
        expect(result.failure).to eq('User not found')
      end
    end

    context 'when custom view not found' do
      let(:params) { { actor_id: user.id, organization_id: user.organization_id, id: 'non-existent-id' } }

      it 'returns failure' do
        expect(result).to be_failure
        expect(result.failure).to eq('Custom view not found')
      end
    end

    context 'when valid params are provided' do
      let(:params) { { actor_id: user.id, organization_id: user.organization_id, id: custom_view.id } }

      it 'returns success with custom view details' do
        expect(result).to be_success
        expect(result.success).to be_a(Entities::CustomView::Detail)
        expect(result.success.name).to eq('Custom view')
        expect(result.success.icon).to eq('icon')
        expect(result.success.filters).to eq([
                                               { 'field' => 'division', 'value' => ['6ca4c7ae-145f-47bd-8925-18d4d03702a7', 'bd3828b2-44cd-4d33-bef4-69f6b70e0a5a'].to_json, 'operator' => 'in' },
                                               { 'field' => 'unresponded', 'value' => 'true', 'operator' => 'is' }
                                             ])
      end
    end
  end
end
