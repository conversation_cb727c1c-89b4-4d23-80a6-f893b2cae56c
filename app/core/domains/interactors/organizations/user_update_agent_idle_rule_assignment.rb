# frozen_string_literal: true

class Interactors::Organizations::UserUpdateAgentIdleRuleAssignment < Interactors::AbstractIteractor
  contract do
    params do
      required(:organization_id).filled(:string)
      required(:enabled).filled(:bool)
      optional(:idle_period).filled(:integer)
      optional(:send_mekari_webhook).filled(:bool)
    end

    rule(:idle_period).validate(:idle_period_validation)

    register_macro(:idle_period_validation) do
      if values[:enabled].to_s.eql?('true')
        if values[:idle_period].present?
          key.failure('idle_period value must above 0') if values[:idle_period] <= 0
        else
          key.failure('idle_period must be filled if enabled')
        end
      end
    end
  end

  include Dry::Monads::Do.for(:result)

  def result
    params = yield result_of_validating_params
    Repositories::Organizations::UpdateAgentIdleRuleAssignment.call(params[:organization_id], params: params)
  end
end
