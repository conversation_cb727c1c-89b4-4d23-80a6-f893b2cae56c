# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Interactors::Organizations::UserUpdateAgentIdleRuleAssignment do
  def result_for(parameters)
    described_class.new(described_class.parameters(parameters)).result
  end

  def value_of(result)
    result.value_or(&:itself)
  end

  describe 'contract testing' do
    context 'when enables agent idle rules assignment without idle_period' do
      it 'return failure ', :aggregate_failures do
        organization = create(:org)
        result       = result_for(organization_id: organization.id, enabled: true)

        expect(result).to be_failure
      end
    end

    context 'when enables agent idle rules assignment with idle_period' do
      before do
        mock_repo = instance_double(Repositories::Organizations::UpdateAgentIdleRuleAssignment)
        allow(Repositories::Organizations::UpdateAgentIdleRuleAssignment).to receive(:new).and_return(mock_repo)
        allow(mock_repo).to receive(:call).and_return(Dry::Monads::Success('success update'))
      end

      it 'return success ', :aggregate_failures do
        organization = create(:org)
        result       = result_for(organization_id: organization.id, enabled: true, idle_period: 10)

        expect(result).to be_success
      end
    end

    context 'when disable agent idle rules assignment withoud idle_period' do
      before do
        mock_repo = instance_double(Repositories::Organizations::UpdateAgentIdleRuleAssignment)
        allow(Repositories::Organizations::UpdateAgentIdleRuleAssignment).to receive(:new).and_return(mock_repo)
        allow(mock_repo).to receive(:call).and_return(Dry::Monads::Success('success update'))
      end

      it 'return success ', :aggregate_failures do
        organization = create(:org)
        result       = result_for(organization_id: organization.id, enabled: false)

        expect(result).to be_success
      end
    end
  end

  describe 'integration test' do
    context 'when enables agent idle rules assignment with idle_period' do
      it 'return success ', :aggregate_failures do
        organization = create(:org)
        result       = result_for(organization_id: organization.id, enabled: true, idle_period: 10)

        expect(result).to be_success
      end
    end

    context 'when disable agent idle rules assignment withoud idle_period' do
      it 'return success ', :aggregate_failures do
        organization = create(:org)
        result       = result_for(organization_id: organization.id, enabled: false)

        expect(result).to be_success
      end
    end

    context 'when enables agent idle with send_mekari_webhook' do
      it 'return success ', :aggregate_failures do
        organization = create(:org)
        result       = result_for(organization_id: organization.id, enabled: true, idle_period: 10, send_mekari_webhook: true)

        expect(result).to be_success
      end
    end
  end
end
