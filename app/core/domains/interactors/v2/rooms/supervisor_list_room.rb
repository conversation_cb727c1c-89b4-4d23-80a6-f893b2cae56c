# frozen_string_literal: true

class Interactors::V2::Rooms::SupervisorListRoom < Interactors::AbstractIteractor
  contract do
    params(Interactors::AbstractIteractor.pagination_schema) do
      required(:organization_id).filled(:string)
      optional(:user_ids).maybe(:array)
      optional(:status).maybe(:string, included_in?: %w[assigned unassigned resolved deleted])
      optional(:sessions).array(:string, included_in?: %w[open expiring expired])
      optional(:tags).array(:string)
      optional(:channels).array(:string, included_in?: Models::ChannelIntegration.known_channels)
      optional(:untagged).filled(:bool)
      optional(:user_id).filled(:string)
      optional(:start_date).maybe(:time)
      optional(:end_date).maybe(:time)
      optional(:time_offsets).maybe(:integer)
      optional(:response_status).array(:string, included_in?: %w[unresponded responded])
      optional(:sort_by).maybe(:string, included_in?: %w[newest oldest])
      optional(:division_ids).array(:string)
      optional(:channel_integration_ids).array(:string)
    end
  end

  include Dry::Monads[:result]
  include Dry::Monads::Do.for(:result)

  def result
    params = yield result_of_validating_params
    params[:is_unresponded] = nil

    if params[:response_status].present? && params[:response_status].size.eql?(1) && (%w[unresponded responded] & params[:response_status]).present?
      params[:is_unresponded] = params[:response_status].last.eql?('unresponded')
    end

    if params[:status].blank?
      params[:status] = %w[assigned unassigned resolved deleted]
    end
    params[:agent_ids]      = params[:user_ids] if params[:user_ids].present?
    user                    = find_user params[:user_id]

    spv_filter_division_enabled = false
    if user.present?
      if user.role.eql?('supervisor')
        spv_filter_division_enabled = Services::Preference.new.enabled?(:spv_filter_division, organization_id: params[:organization_id])
      end

      user_divisions        = get_user_divisions(user)
      params[:actor_role]   = user.role
      params[:division_ids] = get_division_ids(params, user, spv_filter_division_enabled, user_divisions)
      params[:is_admin]     = user.role.eql?('admin') || user.role.eql?('owner')
      params[:channel_ids]  = get_channel_integration_ids(params, user, spv_filter_division_enabled, user_divisions)
    end

    list_room(**params.except(:user_id, :user_ids, :response_status, :channel_integration_ids))
  end

  private

  # Returns an array of channel integration IDs that the user has access to based on their role and divisions.
  def get_channel_integration_ids(params, user, spv_filter_division_enabled, user_divisions)
    params[:channel_integration_ids] ||= []
    valid_channel_integration_ids = []

    if is_admin?(user) || (is_supervisor?(user) && spv_filter_division_enabled && (user.extra&.dig('type_access_inbox') || 'all') == 'all')
      # Admin and supervisors with full access can access all channels, but include channels from specified divisions if provided
      channel_ids = []
      channel_ids = Models::ChannelDivision.where(division_id: params[:division_ids]).pluck(:channel_id) if params[:division_ids].present?
      valid_channel_integration_ids = params[:channel_integration_ids] + channel_ids
      valid_channel_integration_ids = valid_channel_integration_ids.uniq
    else
      # Supervisors can only access channels from their assigned divisions
      channel_division = get_channel_division(user_divisions)
      if channel_division.present?
        if params[:channel_integration_ids].present? && params[:division_ids].present?
          # If both channel integration IDs and division IDs are provided, filter them by the channels in the divisions
          # and ensure they are valid channels in those divisions
          channel_ids = channel_division.pluck(:channel_id).uniq
          valid_channel_integration_ids = params[:channel_integration_ids] + get_channel_id_by_divisions(channel_division, params[:division_ids] || [])
          valid_channel_integration_ids &= channel_ids
        elsif params[:channel_integration_ids].present?
          # If only channel integration IDs are provided, filter them by the channels in the divisions
          valid_channel_integration_ids = params[:channel_integration_ids] & channel_division.pluck(:channel_id).uniq
        elsif params[:division_ids].present?
          # If only division IDs are provided, get the channel IDs from those divisions
          valid_channel_integration_ids = get_channel_id_by_divisions(channel_division, params[:division_ids] || [])
        else
          # If no channel integration IDs or division IDs are provided, use channels from the divisions
          valid_channel_integration_ids = channel_division.pluck(:channel_id).uniq
        end
        valid_channel_integration_ids = valid_channel_integration_ids.uniq
      else
        # If no channel divisions are found, supervisors will treat like admins
        # and can access channels from specified divisions
        channel_ids = []
        channel_ids = Models::ChannelDivision.where(division_id: params[:division_ids]).pluck(:channel_id) if params[:division_ids].present?
        valid_channel_integration_ids = params[:channel_integration_ids] + channel_ids
      end
    end

    valid_channel_integration_ids
  end

  # Returns an array of division IDs that the user has access to based on their role and divisions.
  def get_division_ids(params, user, spv_filter_division_enabled, user_divisions)
    return params[:division_ids] || [] if is_admin?(user)

    if is_supervisor?(user) && spv_filter_division_enabled
      return params[:division_ids] || [] if (user.extra&.dig('type_access_inbox') || 'all') == 'all'
    end

    user_division_ids = user_divisions.pluck(:id)

    valid_division_ids = []
    if params[:division_ids].present?
      if user_division_ids.present?
        # Filter params[:division_ids] to only include valid division IDs that the user has access to
        valid_division_ids = params[:division_ids] & user_division_ids
      else
        # If the user has no divisions, supervisors will treat like admins
        # and can access specified divisionss
        valid_division_ids = params[:division_ids]
      end
    end

    valid_division_ids
  end

  def list_room(*args)
    Repositories::V2::Rooms::All.new(*args).call
  end

  def find_user(user_id)
    Models::User.includes(:divisions).find_by(id: user_id)
  end

  def is_admin?(user)
    user.role.eql?('admin') || user.role.eql?('owner')
  end

  def is_supervisor?(user)
    user.role.eql?('supervisor')
  end

  def get_channel_division(user_divisions)
    channel_division = []
    channel_division = Models::ChannelDivision.where(division_id: user_divisions.pluck(:id)) if user_divisions.present?
    channel_division
  end

  def get_user_divisions(user)
    user.divisions.filter { |division| division.name != 'General' }
  end

  def get_channel_id_by_divisions(channel_division, division_ids)
    return [] unless division_ids.present?
    channel_division.select { |cd| division_ids.include?(cd.division_id) }.pluck(:channel_id).uniq
  end
end
