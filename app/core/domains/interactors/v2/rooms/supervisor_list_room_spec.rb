# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Interactors::V2::Rooms::SupervisorListRoom, es_refresh_index: true do
  before(:context) do
    @organization = create :organization
    @wa_channel = create :channel_wa, organization_id: @organization.id
    @ig_channel = create :ig_channel, organization: @organization
    @fb_channel = create :fb_channel, organization: @organization
    @supervisor = create :supervisor0, organization: @organization
    @division = create :agent_division, organization_id: @organization.id
    @general_division = create :division_general, organization_id: @organization.id
    @other_division = create :agent_division, name: 'Other Division', organization_id: @organization.id

    Services::Preference.new.add :force_recache_es_index_room_cache, title: 'Recache ES room cache index', extra: {}, target: 'feature'
    Services::Preference.new.enable :force_recache_es_index_room_cache

    Services::Preference.new.add :force_recache_es_index_message, title: 'Recache ES message index', extra: {}, target: 'feature'
    Services::Preference.new.enable :force_recache_es_index_message

    create_rooms
  end

  after(:context) do
    DatabaseCleaner.clean
    create_index(Elasticsearch::RoomRepository, force: true)
  end

  let(:repositories_rooms_all) { Repositories::V2::Rooms::All.new(params) }
  let(:usecase) { described_class.new(described_class.parameters(params)).result }

  def create_messages(room, i, session_at)
    resource = build_stubbed :text_message, type: 'text', text: "Lorem ipsum No-#{i}", room: room, created_at: session_at, organization: @organization
    Services::Elasticsearch::Rooms::SetLastMessage.new(message: resource).call
    Services::Elasticsearch::Rooms::SetSessionAt.new(message: resource).call
    resource
  end

  def create_rooms
    cur_time = Time.zone.now
    rooms    = []
    messages = []
    (1..9).map do |i|
      case i
      when 1..2
        room = create :room, name: "RoomNo#{i}", status: 'unassigned', organization_id: @organization.id, channel_integration_id: @ig_channel.id
      when 8..9
        room = create :room, name: "RoomNo#{i}", status: 'unassigned', organization_id: @organization.id, channel_integration_id: @wa_channel.id
      else
        room = create :room, name: "RoomNo#{i}", status: 'unassigned', organization_id: @organization.id, channel_integration_id: @wa_channel.id, division_id: @general_division.id
      end
      room.save

      messages << create_messages(room, 1, cur_time + i.minute)
      rooms << room
    end
    rooms
  end

  describe 'list rooms when user is supervisor' do
    describe 'spv_filter_division preference enabled and type_access_inbox is division' do
      before do
        Services::Preference.new.add :spv_filter_division, title: 'Supervisor filter division', extra: {}, target: 'feature'
        Services::Preference.new.enable :spv_filter_division
        Services::Preference.new.set_organization_ids :spv_filter_division, [@organization.id]

        @supervisor.extra = { type_access_inbox: 'division_room_only' }
        @supervisor.save
      end

      def define_division_relation
        create :user_division, division_id: @division.id, user_id: @supervisor.id
        create :channel_divisions, division_id: @division.id, channel_id: @ig_channel.id
      end

      context 'when params channel_integration_ids and division_ids are not provided' do
        before do
          define_division_relation
        end

        let(:params) {
          {
            organization_id: @organization.id,
            user_id:         @supervisor.id,
            is_admin:        false
          }
        }

        it 'returns rooms related to supervisor division', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(2)
        end
      end

      context 'when params channel_integration_ids provided' do
        before do
          define_division_relation
          create :channel_divisions, division_id: @division.id, channel_id: @wa_channel.id
        end

        let(:params) {
          {
            organization_id:         @organization.id,
            user_id:                 @supervisor.id,
            is_admin:                false,
            channel_integration_ids: [@wa_channel.id]
          }
        }

        it 'returns rooms with specified channel integration', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(7)
        end
      end

      context 'when params division_ids provided' do
        before do
          define_division_relation

          create :user_division, division_id: @other_division.id, user_id: @supervisor.id
          create :channel_divisions, division_id: @other_division.id, channel_id: @wa_channel.id
        end

        let(:params) {
          {
            organization_id: @organization.id,
            user_id:         @supervisor.id,
            is_admin:        false,
            division_ids:    [@other_division.id]
          }
        }

        it 'returns rooms with specified division', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(7)
        end
      end

      context 'when params channel_integration_ids and division_ids provided' do
        before do
          define_division_relation

          create :user_division, division_id: @other_division.id, user_id: @supervisor.id
          create :channel_divisions, division_id: @other_division.id, channel_id: @wa_channel.id
        end

        let(:params) {
          {
            organization_id:         @organization.id,
            user_id:                 @supervisor.id,
            is_admin:                false,
            channel_integration_ids: [@ig_channel.id],
            division_ids:            [@other_division.id]
          }
        }

        it 'returns rooms with specified channel integration and division', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(9)
        end
      end
    end

    describe 'spv_filter_division preference enabled and type_access_inbox is all' do
      before do
        Services::Preference.new.add :spv_filter_division, title: 'Supervisor filter division', extra: {}, target: 'feature'
        Services::Preference.new.enable :spv_filter_division
        Services::Preference.new.set_organization_ids :spv_filter_division, [@organization.id]

        @supervisor.extra = { type_access_inbox: 'all' }
        @supervisor.save
      end

      context 'when params channel_integration_ids and division_ids are not provided' do
        let(:params) {
          {
            organization_id: @organization.id,
            user_id:         @supervisor.id,
            is_admin:        false
          }
        }

        it 'returns all rooms', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(9)
        end
      end

      context 'when params channel_integration_ids provided' do
        let(:params) {
          {
            organization_id:         @organization.id,
            user_id:                 @supervisor.id,
            is_admin:                false,
            channel_integration_ids: [@wa_channel.id]
          }
        }

        it 'returns rooms with specified channel integration', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(7)
        end
      end

      context 'when params division_ids provided' do
        before do
          create :channel_divisions, division_id: @division.id, channel_id: @ig_channel.id
        end

        let(:params) {
          {
            organization_id: @organization.id,
            user_id:         @supervisor.id,
            is_admin:        false,
            division_ids:    [@division.id]
          }
        }

        it 'returns rooms with specified division', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(2)
        end
      end

      context 'when params channel_integration_ids and division_ids provided' do
        before do
          create :channel_divisions, division_id: @division.id, channel_id: @ig_channel.id
        end

        let(:params) {
          {
            organization_id:         @organization.id,
            user_id:                 @supervisor.id,
            is_admin:                false,
            channel_integration_ids: [@wa_channel.id],
            division_ids:            [@division.id]
          }
        }

        it 'returns rooms with specified channel integration and division', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(9)
        end
      end
    end

    describe 'spv_filter_division preference disable and he does not assigned to any divisions' do
      context 'when params channel_integration_ids and division_ids are not provided' do
        let(:params) {
          {
            organization_id: @organization.id,
            user_id:         @supervisor.id,
            is_admin:        false
          }
        }

        it 'returns all rooms', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(9)
        end
      end

      context 'when params channel_integration_ids provided' do
        let(:params) {
          {
            organization_id:         @organization.id,
            user_id:                 @supervisor.id,
            is_admin:                false,
            channel_integration_ids: [@wa_channel.id]
          }
        }

        it 'returns rooms with specified channel integration', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(7)
        end
      end

      context 'when params division_ids provided' do
        before do
          create :channel_divisions, division_id: @division.id, channel_id: @ig_channel.id
        end

        let(:params) {
          {
            organization_id: @organization.id,
            user_id:         @supervisor.id,
            is_admin:        false,
            division_ids:    [@division.id]
          }
        }

        it 'returns rooms with specified division', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(2)
        end
      end

      context 'when params channel_integration_ids and division_ids provided' do
        before do
          create :channel_divisions, division_id: @division.id, channel_id: @ig_channel.id
        end

        let(:params) {
          {
            organization_id:         @organization.id,
            user_id:                 @supervisor.id,
            is_admin:                false,
            channel_integration_ids: [@wa_channel.id],
            division_ids:            [@division.id]
          }
        }

        it 'returns rooms with specified channel integration and division', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(9)
        end
      end
    end

    describe 'spv_filter_division preference disable and he assigned to divisions' do
      before do
        create :user_division, division_id: @division.id, user_id: @supervisor.id
        create :channel_divisions, division_id: @division.id, channel_id: @ig_channel.id
      end

      context 'when params channel_integration_ids and division_ids are not provided' do
        let(:params) {
          {
            organization_id: @organization.id,
            user_id:         @supervisor.id,
            is_admin:        false
          }
        }

        it 'returns rooms related to supervisor division' do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(2)
        end
      end

      context 'when params channel_integration_ids provided' do
        let(:params) {
          {
            organization_id:         @organization.id,
            user_id:                 @supervisor.id,
            is_admin:                false,
            channel_integration_ids: [@ig_channel.id]
          }
        }

        it 'returns rooms with specified channel integration', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(2)
        end
      end

      context 'when params division_ids provided' do
        let(:params) {
          {
            organization_id: @organization.id,
            user_id:         @supervisor.id,
            is_admin:        false,
            division_ids:    [@division.id]
          }
        }

        it 'returns rooms with specified division', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(2)
        end
      end

      context 'when params channel_integration_ids and division_ids provided' do
        let(:params) {
          {
            organization_id:         @organization.id,
            user_id:                 @supervisor.id,
            is_admin:                false,
            channel_integration_ids: [@ig_channel.id],
            division_ids:            [@other_division.id]
          }
        }

        it 'returns rooms with specified channel integration and division', :aggregate_failures do
          expect(usecase).to be_success
          expect(usecase.success.response.size).to eql(2)
        end
      end
    end
  end

  describe 'list rooms when user is admin' do
    context 'when params channel_integration_ids and division_ids are not provided' do
      let(:admin) { create :admin, organization: @organization }
      let(:params) {
        {
          organization_id: @organization.id,
          user_id:         admin.id,
          is_admin:        true
        }
      }

      it 'returns all rooms', :aggregate_failures do
        expect(usecase).to be_success
        expect(usecase.success.response.size).to eql(9)
      end
    end

    context 'when params channel_integration_ids provided' do
      let(:admin) { create :admin, organization: @organization }
      let(:params) {
        {
          organization_id:         @organization.id,
          user_id:                 admin.id,
          is_admin:                true,
          channel_integration_ids: [@wa_channel.id]
        }
      }

      it 'returns rooms with specified channel integration', :aggregate_failures do
        expect(usecase).to be_success
        expect(usecase.success.response.size).to eql(7)
      end
    end

    context 'when params division_ids provided' do
      before do
        create :channel_divisions, division_id: @division.id, channel_id: @ig_channel.id
      end

      let(:admin) { create :admin, organization: @organization }
      let(:params) {
        {
          organization_id: @organization.id,
          user_id:         admin.id,
          is_admin:        true,
          division_ids:    [@division.id]
        }
      }

      it 'returns rooms with specified division', :aggregate_failures do
        expect(usecase).to be_success
        expect(usecase.success.response.size).to eql(2)
      end
    end

    context 'when params channel_integration_ids and division_ids provided' do
      before do
        create :channel_divisions, division_id: @division.id, channel_id: @ig_channel.id
      end

      let(:admin) { create :admin, organization: @organization }
      let(:params) {
        {
          organization_id:         @organization.id,
          user_id:                 admin.id,
          is_admin:                true,
          channel_integration_ids: [@wa_channel.id],
          division_ids:            [@division.id]
        }
      }

      it 'returns rooms with specified channel integration and division', :aggregate_failures do
        expect(usecase).to be_success
        expect(usecase.success.response.size).to eql(9)
      end
    end
  end
end
