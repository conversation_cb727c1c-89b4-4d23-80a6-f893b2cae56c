# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Interactors::V2::Rooms::AgentListRoom do
  let(:repositories_rooms_all) { Repositories::V2::Rooms::All.new(params) }
  let(:organization) { create :organization }
  let(:user) { create :agent0, :searchable, organization: organization }
  let(:division) { create :agent_division, organization: organization }
  let(:wa_channel) { create :channel_wa, organization: organization }

  describe 'list rooms' do
    context 'when params channel_integration_ids and division_ids are not provided' do
      before do
        division.channels = [wa_channel]
        user.divisions    = [division]
      end

      let(:params) {
        {
          organization_id: organization.id,
          agent_ids:       [user.id],
          status:          %w[assigned resolved deleted],
          channel_ids:     [wa_channel.id],
          division_ids:    [],  # Add this since the interactor always sets division_ids
          is_unresponded:  true,
          actor_role:      'agent'
        }
      }
      it 'returns array of rooms', :aggregate_failures do
        # stub class
        allow(Repositories::V2::Rooms::All).to receive(:new).with(params).and_return(Repositories::V2::Rooms::All.new(organization_id: organization.id))
        # stub parameters has been assigned to class
        allow(Repositories::V2::Rooms::All.new(organization_id: organization.id, actor_role: 'agent', agent_ids: [user.id], channel_ids: [wa_channel.id], status: %w[assigned resolved deleted], division_ids: [], is_unresponded: true)).to receive(:call).and_return('success')

        # expectation
        expect(usecase(organization_id: organization.id, user_id: user.id, response_status: ['unresponded'])).to eql('success')
      end
    end

    context 'when params channel_integration_ids provided' do
      before do
        division.channels = [wa_channel]
        user.divisions    = [division]
      end

      let(:params) {
        {
          organization_id: organization.id,
          agent_ids:       [user.id],
          status:          %w[assigned resolved deleted],
          channel_ids:     [wa_channel.id],
          division_ids:    [],
          is_unresponded:  true,
          actor_role:      'agent'
        }
      }

      it 'returns array of rooms', :aggregate_failures do
        # stub class
        allow(Repositories::V2::Rooms::All).to receive(:new).with(params).and_return(Repositories::V2::Rooms::All.new(organization_id: organization.id))
        # stub parameters has been assigned to class
        allow(Repositories::V2::Rooms::All.new(organization_id: organization.id, actor_role: 'agent', agent_ids: [user.id], channel_ids: [wa_channel.id], status: %w[assigned resolved deleted], division_ids: [], is_unresponded: true)).to receive(:call).and_return('success')

        # expectation
        expect(usecase(organization_id: organization.id, user_id: user.id, response_status: ['unresponded'], channel_integration_ids: [wa_channel.id])).to eql('success')
      end
    end

    context 'when params division_ids provided' do
      before do
        division.channels = [wa_channel]
        user.divisions    = [division]
      end

      let(:params) {
        {
          organization_id: organization.id,
          agent_ids:       [user.id],
          status:          %w[assigned resolved deleted],
          channel_ids:     [wa_channel.id],
          division_ids:    [division.id],
          is_unresponded:  true,
          actor_role:      'agent'
        }
      }

      it 'returns array of rooms', :aggregate_failures do
        # stub class
        allow(Repositories::V2::Rooms::All).to receive(:new).with(params).and_return(Repositories::V2::Rooms::All.new(organization_id: organization.id))
        # stub parameters has been assigned to class
        allow(Repositories::V2::Rooms::All.new(organization_id: organization.id, actor_role: 'agent', agent_ids: [user.id], channel_ids: [wa_channel.id], status: %w[assigned resolved deleted], division_ids: [division.id], is_unresponded: true)).to receive(:call).and_return('success')

        # expectation
        expect(usecase(organization_id: organization.id, user_id: user.id, division_ids: [division.id], response_status: ['unresponded'])).to eql('success')
      end
    end

    context 'when params channel_integration_ids and division_ids provided' do
      before do
        division.channels = [wa_channel]
        user.divisions    = [division]
      end

      let(:params) {
        {
          organization_id: organization.id,
          agent_ids:       [user.id],
          status:          %w[assigned resolved deleted],
          channel_ids:     [wa_channel.id],
          division_ids:    [division.id],
          is_unresponded:  true,
          actor_role:      'agent'
        }
      }

      it 'returns array of rooms', :aggregate_failures do
        # stub class
        allow(Repositories::V2::Rooms::All).to receive(:new).with(params).and_return(Repositories::V2::Rooms::All.new(organization_id: organization.id))
        # stub parameters has been assigned to class
        allow(Repositories::V2::Rooms::All.new(organization_id: organization.id, actor_role: 'agent', agent_ids: [user.id], channel_ids: [wa_channel.id], status: %w[assigned resolved deleted], division_ids: [division.id], is_unresponded: true)).to receive(:call).and_return('success')

        # expectation
        expect(usecase(organization_id: organization.id, user_id: user.id, division_ids: [division.id], response_status: ['unresponded'], channel_integration_ids: [wa_channel.id])).to eql('success')
      end
    end
  end

  def usecase params
    described_class.new(described_class.parameters(params)).result
  end
end
