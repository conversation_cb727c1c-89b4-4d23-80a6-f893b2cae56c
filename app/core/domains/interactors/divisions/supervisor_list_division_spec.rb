# frozen_string_literal: true

require 'rails_helper'
RSpec.describe Interactors::Divisions::SupervisorListDivision, preference: true do
  let(:model_organization) { create :organization }
  let(:model_division) { create :agent_division, name: 'divisi satu', organization: model_organization }
  let(:model_division_2) { create :agent_division, name: 'divisi dua', organization: model_organization }
  let(:model_division_3) { create :agent_division, name: 'divisi tiga', organization: model_organization }
  let(:user) { create :supervisor0, organization: model_organization }
  let(:channel) { create :wa_channel, organization: model_organization, settings: { account_name: 'wahatsapp_1' } }
  let(:ig_channel) { create :ig_channel, organization: model_organization }

  def create_agent_division(agent_division:)
    org_id = model_organization.id
    (1..agent_division).each do |i|
      create :agent_division, :searchable, name: "agentDiv#{i}", organization_id: org_id, default: false
    end
  end

  let(:get_agent_div) { described_class.new(described_class.parameters(organization_id: model_organization.id)).result }
  let(:get_spv_div) { described_class.new(described_class.parameters(organization_id: model_organization.id, actor_id: spv1.id, role: 'supervisor')).result }

  describe 'list agent division' do
    context 'when fetching agent division' do
      before do
        ElasticsearchQuieter.call do
          create_agent_division(agent_division: 3)
        end
      end

      it 'returns agent division' do
        expect(get_agent_div.success.response.count).to eql 3
      end
    end

    context 'when fetching agent division exclude general' do
      before do
        create(:agent_division, name: 'General', organization: model_organization)

        ElasticsearchQuieter.call do
          create_agent_division(agent_division: 3)
        end
      end

      let(:params_exclude_general) do
        {
          organization_id: model_organization.id,
          exclude_general: true
        }
      end
      let(:exclude_general_usecase) { described_class.new(described_class.parameters(params_exclude_general)).result }

      it 'returns agent division' do
        expect(exclude_general_usecase.success.response.count).to eql 3
      end
    end

    context 'when fetching agent division' do
      let(:division1) { create :agent_division, organization_id: model_organization.id }
      let(:admin) { create :admin0, organization: model_organization }
      let(:spv1) { create :supervisor0, organization: model_organization }

      before do
        create_enable_preference(:reporting_division_filter, 'Reporting division filter', target: 'feature')
        create :user_division, division_id: division1.id, user_id: spv1.id

        ElasticsearchQuieter.call do
          create_agent_division(agent_division: 3)
        end
      end

      it 'returns agent division' do
        expect(get_spv_div.success.response.count).to eql 1
      end
    end

    context 'when fetching division with user and channel' do
      before do
        model_division.users    = [user]
        model_division.channels = [channel]
        model_division.__elasticsearch__.index_document refresh: true
      end

      it 'returns agent division', :aggregate_failures do
        expect(get_agent_div.success.response.count).to eql 1
        expect(get_agent_div.success.response.last.users).to include(
                                                               id:        user.id,
                                                               full_name: user.full_name,
                                                               role:      user.role,
                                                               email:     user.email
                                                        )
        expect(get_agent_div.success.response.last.channels).to include(
                                                                  id:             channel.id,
                                                                  target_channel: channel.target_channel,
                                                                  channel_name:   'wahatsapp_1'
                                                        )
      end
    end

    context 'when fetching division with filter' do
      let(:params_channel_id) do
        {
          organization_id: model_organization.id,
          channel_id:      channel.id
        }
      end
      let(:filter_channel_id_usecase) { described_class.new(described_class.parameters(params_channel_id)).result }

      let(:params_target_channels) do
        {
          organization_id: model_organization.id,
          target_channels: ['ig']
        }
      end
      let(:filter_target_channels_usecase) { described_class.new(described_class.parameters(params_target_channels)).result }

      before do
        model_division.users      = [user]
        model_division.channels   = [channel]
        model_division_2.users    = [user]
        model_division_2.channels = [ig_channel]
        model_division_3.users    = [user]
        bulk_index([model_division, model_division_2, model_division_3], recreate_index: false, refresh: true)
      end

      it 'returns division with correct channel_id and empty channel', :aggregate_failures do
        expect(filter_channel_id_usecase.success.response.count).to eql 2
        expect(filter_channel_id_usecase.success.response.pluck(:name).sort).to eql ['divisi satu', 'divisi tiga']
      end

      it 'returns division with correct target channel and empty channel', :aggregate_failures do
        expect(filter_target_channels_usecase.success.response.count).to eql 2
        expect(filter_target_channels_usecase.success.response.pluck(:name).sort).to eql ['divisi dua', 'divisi tiga']
      end
    end

    context 'when assigned_to_me is true but actor_id is not provided' do
      let(:params) do
        {
          organization_id: model_organization.id,
          assigned_to_me:  true
        }
      end

      let(:usecase) { described_class.new(described_class.parameters(params)).result }

      it 'returns failure with error message', :aggregate_failures do
        expect(usecase.failure).to eql 'Actor ID is required for assigned_to_me filter'
      end
    end

    context 'when assigned_to_me is true and user is admin' do
      let(:admin) { create :admin0, organization: model_organization }
      let(:params) do
        {
          organization_id: model_organization.id,
          assigned_to_me:  true,
          actor_id:        admin.id
        }
      end

      before do
        ElasticsearchQuieter.call do
          create_agent_division(agent_division: 3)
        end
      end

      let(:usecase) { described_class.new(described_class.parameters(params)).result }

      it 'returns all divisions', :aggregate_failures do
        expect(usecase.success.response.count).to eql 3
        expect(usecase.success.response.pluck(:name).sort).to eql ['agentDiv1', 'agentDiv2', 'agentDiv3']
      end
    end

    context 'when assigned_to_me is true and user is supervisor but spv_filter_division is not enabled' do
      let(:spv1) { create :supervisor0, organization: model_organization }
      let(:params) do
        {
          organization_id: model_organization.id,
          assigned_to_me:  true,
          actor_id:        spv1.id
        }
      end

      before do
        ElasticsearchQuieter.call do
          create_agent_division(agent_division: 3)
        end
      end

      let(:usecase) { described_class.new(described_class.parameters(params)).result }

      it 'returns all divisions', :aggregate_failures do
        expect(usecase.success.response.count).to eql 3
        expect(usecase.success.response.pluck(:name).sort).to eql ['agentDiv1', 'agentDiv2', 'agentDiv3']
      end
    end

    context 'when assigned_to_me is true and user is supervisor but spv_filter_division is not enabled' do
      let(:spv1) { create :supervisor0, organization: model_organization }
      let(:params) do
        {
          organization_id: model_organization.id,
          assigned_to_me:  true,
          actor_id:        spv1.id
        }
      end

      before do
        ElasticsearchQuieter.call do
          create_agent_division(agent_division: 3)
        end
      end

      let(:usecase) { described_class.new(described_class.parameters(params)).result }

      it 'returns all divisions', :aggregate_failures do
        expect(usecase.success.response.count).to eql 3
        expect(usecase.success.response.pluck(:name).sort).to eql ['agentDiv1', 'agentDiv2', 'agentDiv3']
      end
    end

    context 'when assigned_to_me is true and user is supervisor but spv_filter_division is not enabled and he assigned the divisions' do
      let(:spv1) { create :supervisor0, organization: model_organization }
      let(:params) do
        {
          organization_id: model_organization.id,
          assigned_to_me:  true,
          actor_id:        spv1.id
        }
      end

      before do
        ElasticsearchQuieter.call do
          create_agent_division(agent_division: 3)
        end

        model_division.users      = [spv1]
        model_division_2.users    = [user]
        model_division_3.users    = [user]
        bulk_index([model_division, model_division_2, model_division_3], recreate_index: false, refresh: true)
      end

      let(:usecase) { described_class.new(described_class.parameters(params)).result }

      it 'returns divisions assigned to the user', :aggregate_failures do
        expect(usecase.success.response.count).to eql 1
        expect(usecase.success.response.pluck(:name)).to include 'divisi satu'
      end
    end

    context 'when assigned_to_me is true and user is supervisor with type_access_inbox set to division_only' do
      let(:spv1) { create :supervisor0, organization: model_organization, extra: { 'type_access_inbox' => 'division_only' } }
      let(:params) do
        {
          organization_id: model_organization.id,
          assigned_to_me:  true,
          actor_id:        spv1.id
        }
      end

      before do
        Services::Preference.new.add :spv_filter_division, title: 'chat access flag', target: 'feature'
        Services::Preference.new.enable :spv_filter_division
        Services::Preference.new.set_organization_ids :spv_filter_division, [model_organization.id]

        ElasticsearchQuieter.call do
          create_agent_division(agent_division: 3)
        end

        model_division.users      = [spv1]
        model_division_2.users    = [user]
        model_division_3.users    = [user]
        bulk_index([model_division, model_division_2, model_division_3], recreate_index: false, refresh: true)
      end

      let(:usecase) { described_class.new(described_class.parameters(params)).result }

      it 'returns divisions assigned to the user', :aggregate_failures do
        expect(usecase.success.response.count).to eql 1
        expect(usecase.success.response.pluck(:name)).to include 'divisi satu'
      end
    end

    context 'when assigned_to_me is true and user is supervisor with type_access_inbox set to all' do
      let(:spv1) { create :supervisor0, organization: model_organization, extra: { 'type_access_inbox' => 'all' } }
      let(:params) do
        {
          organization_id: model_organization.id,
          assigned_to_me:  true,
          actor_id:        spv1.id
        }
      end

      before do
        Services::Preference.new.add :spv_filter_division, title: 'chat access flag', target: 'feature'
        Services::Preference.new.enable :spv_filter_division
        Services::Preference.new.set_organization_ids :spv_filter_division, [model_organization.id]

        ElasticsearchQuieter.call do
          create_agent_division(agent_division: 3)
        end
      end

      let(:usecase) { described_class.new(described_class.parameters(params)).result }

      it 'returns all divisions', :aggregate_failures do
        expect(usecase.success.response.count).to eql 3
        expect(usecase.success.response.pluck(:name).sort).to eql ['agentDiv1', 'agentDiv2', 'agentDiv3']
      end
    end

    context 'when assigned_to_me is true and user is agent' do
      let(:agent) { create :agent0, organization: model_organization }
      let(:params) do
        {
          organization_id: model_organization.id,
          assigned_to_me:  true,
          actor_id:        agent.id
        }
      end

      before do
        Services::Redis::Divisions::ResetChannelsByUser.new(agent.id, channel_ids: [channel.id, ig_channel.id]).call

        ElasticsearchQuieter.call do
          create_agent_division(agent_division: 3)
        end

        model_division.users      = [agent]
        model_division_2.users    = [agent]
        model_division_3.users    = [user]
        bulk_index([model_division, model_division_2, model_division_3], recreate_index: false, refresh: true)
      end

      let(:usecase) { described_class.new(described_class.parameters(params)).result }

      it 'returns divisions assigned to the user', :aggregate_failures do
        expect(usecase.success.response.count).to eql 2
        expect(usecase.success.response.pluck(:name).sort).to eql ['divisi dua', 'divisi satu']
      end
    end
  end
end
