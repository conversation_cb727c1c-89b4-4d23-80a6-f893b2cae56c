# frozen_string_literal: true

class Interactors::Divisions::SupervisorListDivision < Interactors::AbstractIteractor
  contract do
    params(Interactors::AbstractIteractor.pagination_schema) do
      required(:organization_id).filled(:string)
      optional(:is_counted).filled(:bool)
      optional(:target_channels).array(:string, included_in?: Models::ChannelIntegration.known_channels)
      optional(:channel_id).filled(:string)
      optional(:actor_id).filled(:string)
      optional(:role).filled(:string)
      optional(:exclude_general).filled(:bool)
      optional(:assigned_to_me).maybe(:bool)
    end
  end

  include Dry::Monads[:result]
  include Dry::Monads::Do.for(:result)

  def result
    params = yield result_of_validating_params
    params = yield validate_assigned_to_me(params)

    if params[:assigned_to_me] && params[:actor_id].present?
      division_ids = get_division_ids(params)
    else
      division_ids = if filtering_division_enabled(params[:organization_id])
        validate_division(params[:actor_id], params[:role])
      else
        []
      end
    end

    params[:division_ids] = division_ids

    Repositories::Divisions::All.new(params.except(:actor_id, :role, :assigned_to_me)).call
  end

  private

  def get_division_ids(params)
    user = find_user(params[:actor_id])
    return [] if user.role.eql?('admin') || user.role.eql?('owner')

    if user.role.eql?('supervisor') && Services::Preference.new.enabled?(:spv_filter_division, organization_id: params[:organization_id])
      return [] if (user.extra&.dig('type_access_inbox') || 'all') == 'all'
    end

    Services::Redis::Divisions::GetDivisionsByUser.new(params[:actor_id]).call
  rescue => e
    Rails.logger.error("Failed to get channel integration IDs for user #{params[:user_id]}: #{e.message}")
    []
  end

  def validate_assigned_to_me(params)
    return Success params unless params[:assigned_to_me]
    return Failure 'Actor ID is required for assigned_to_me filter' if params[:actor_id].blank?
    Success params
  end

  def find_user(user_id)
    Services::Redis::Users::Get.new(user_id).call
  end
end
