# frozen_string_literal: true

class Interactors::SuperAdminUploadFrontendMenuSettings < Interactors::AbstractIteractor
  contract do
    params do
      required(:file).filled(respond_to?: :path)
    end

    rule(:file) do
      json_file = JSON.parse(File.read(value)).deep_symbolize_keys!
      preference = json_file[:preferences]
      return key.failure('preference is missing') unless preference

      web = preference[:web]
      return key.failure('preference.web is missing') unless web

      super_admin = web[:super_admin]
      return key.failure('preference.web.super_admin is missing') unless super_admin

      admin = web[:admin]
      return key.failure('preference.web.admin is missing') unless admin

      agent = web[:agent]
      return key.failure('preference.web.agent is missing') unless agent

      supervisor = web[:supervisor]
      return key.failure('preference.web.supervisor is missing') unless supervisor

      owner = web[:owner]
      return key.failure('preference.web.owner is missing') unless owner

      member = web[:member]
      return key.failure('preference.web.member is missing') unless member
    end
  end

  include Dry::Monads::Do.for(:result)
  include Dry::Monads[:result]

  def result
    params = yield result_of_validating_params
    yield Repositories::FrontendMenuSettingsUploader.new(file: params[:file]).call
    yield Repositories::SetFrontendMenuSettings.new(params: read_json_file(params[:file])).call

    Success Hashie::Mash.new({ is_success: true })
  end

  private

  def read_json_file(file)
    JSON.parse(File.read(file.tempfile)).deep_symbolize_keys!
  end
end
