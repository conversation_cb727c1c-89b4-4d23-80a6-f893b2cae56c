# frozen_string_literal: true

class Services::Message::CreateMessageFromCampaign
  include Dry::Monads[:result]

  def initialize(message, room_response)
    @message = message
    @room = room_response[:room]
    @contact = room_response[:contact]
    @bot_participant = room_response[:bot_participant]
  end

  def call
    return if @room.nil? || @contact.nil?
    @contact = Models::Contact.safe_new(@contact)
    create_message_from_campaign
  end

  private

  def create_message_from_campaign
    ensure_bot_participant
    return if @bot_participant.nil?

    log_cache = fetch_log_cache
    if log_cache.blank?
      # make sure aaa work when no broadcast log
      auto_assign_agent(@room.id)
      return
    end

    @broadcast = fetch_broadcast(log_cache)
    if @broadcast.blank? || message_exists?(log_cache)
      # make sure aaa work when message already created
      auto_assign_agent(@room.id)
      return
    end

    campaign_message = create_message_from_log(log_cache)
    saved_message = save_message(campaign_message, @room.id, @contact)
    # move here becase updating division from broadcast is after save message
    auto_assign_agent(@room.id)
    handle_saved_message(saved_message, log_cache)
  rescue => e
    handle_error(e)
    nil
  end

  def ensure_bot_participant
    if @bot_participant.present?
      @bot_participant = Models::BotParticipant.new(@bot_participant.as_json)
      return
    end
    result = create_bot_participant
    @bot_participant = result.success? ? result.value! : nil
  end

  def create_bot_participant
    Services::Participants::Bot.new(room_id: @room.id, room_created_at: @room.created_at).call.tap do |result|
      log_bot_participant_error(result) if result.failure?
    end
  end

  def log_bot_participant_error(bot_participant)
    Rollbar.error('[Services::Message::CreateMessageFromCampaign] Failed to create bot_participant', errors: bot_participant.failure, class: self.class, method: __method__)
  end

  def fetch_log_cache
    retention_days = (ENV['BROADCAST_BUBBLE_RETENTION_DAYS'] || 7).to_i
    Services::Redis::BroadcastLogs::Get.new(@contact.organization_id, @contact.account_uniq_id, @contact.channel_integration_id, retention_days.days.ago).call
  end

  def fetch_broadcast(log_cache)
    Services::Redis::Broadcasts::Get.new(log_cache.messages_broadcast_id).call
  end

  def message_exists?(log_cache)
    Models::Message.exists?(external_id: log_cache.external_id, created_at: @broadcast.created_at..Time.now)
  end

  def save_message(message, room_id, contact)
    if message.save
      clean_up_logs(contact.account_uniq_id, contact.channel_integration_id)
      update_room(room_id) if Services::Preference.new.enabled?(:update_division_from_broadcast)
      Success(message)
    else
      Failure(message.errors.full_messages)
    end
  end

  def auto_assign_agent(room_id)
    # Skip auto-assignment for group service rooms
    return if @room.type.eql?('Models::GroupServiceRoom')

    # Check for online agents in a single operation
    online_agent_count = Services::Redis::Organizations::OnlineAgent.new(organization_id: @message[:organization_id]).call.to_i

    # Only publish auto-assign event if online agents are available
    return if online_agent_count <= 0

    # Directly publish the auto-assign event
    Rails.logger.debug("Auto Agent Allocation; room_id: #{room_id}; class: [Services::Message::CreateMessageFromCampaign]")
    Publishers::WaInbounds::AutoAssignAgent.new({ room_id: room_id, organization_id: @message[:organization_id] }).publish
  end

  def update_room(room_id)
    return unless @broadcast.division_id.present?
    room_cache = Elasticsearch::RoomRepository.new
    attrs = { id: room_id, division_id: @broadcast.division_id }

    @room.update_column(:division_id, @broadcast.division_id)
    room_cache.update(attrs, { routing: @broadcast.organization_id })
  end

  def clean_up_logs(account_uniq_id, channel_integration_id)
    Services::Redis::BroadcastLogs::Del.new(channel_integration_id, account_uniq_id).call
  end

  def handle_error(e)
    Rollbar.error(e, class: self.class, method: __method__, args: (method(__method__).parameters.map { |_, ctx_arg| { ctx_arg => binding.local_variable_get(ctx_arg) } } rescue 'err parse args*'))
  end

  def create_message_from_log log_cache
    base_message = build_base_message(log_cache)

    if @broadcast.parameters['header'].present?
      message = case @broadcast.parameters['header']['format']
                when 'IMAGE'
                  Models::ImageMessage.new(base_message)
                when 'DOCUMENT'
                  Models::DocumentMessage.new(base_message)
                when 'VIDEO'
                  Models::VideoMessage.new(base_message)
                else
                  Models::TextMessage.new(base_message)
      end
      if Services::Preference.new.enabled?(:reused_asset_message)
        message_file = Models::MessageFile.find_by({ id: @broadcast.id })
        is_saved = true
        unless message_file.present?
          message_file = Models::MessageFile.find_or_create_by({ id: @broadcast.id, organization_id: @broadcast.organization_id })
          if message_file.present?
            message_file.remote_file_url = @broadcast.parameters['header']['params']['url']
            unless message_file.save
              message.raw_message[:error_messages] = message_file.errors.messages
              is_saved = false
            end
          end
        end
        if ['IMAGE', 'DOCUMENT', 'VIDEO'].include?(@broadcast.parameters['header']['format'])
          if message_file.present?
            message.file_uniq_id = message_file.id
            message.write_attribute(:file, File.basename(message_file.file.url)) if is_saved
          end
        end
      else
        message.remote_file_url = @broadcast.parameters['header']['params']['url']
      end
    else
      message = Models::TextMessage.new(base_message)
    end
    message.participant = @bot_participant
    message
  end

  def build_base_message(log_cache)
    {
      room_id:         @room.id,
      organization_id: @contact.organization_id,
      text:            generate_text(@contact, log_cache),
      sender_type:     @bot_participant.contact_able_type,
      sender_id:       @bot_participant.contact_able_id,
      status:          log_cache.status.nil? || log_cache.status == 'pending' ? 'created' : log_cache.status,
      is_campaign:     true,
      raw_message:     { message_broadcast_log_id: log_cache.id, message_broadcast_id: log_cache.messages_broadcast_id },
      external_id:     log_cache.external_id,
      status_message:  log_cache.messages_response || {},
      created_at:      log_cache.created_at,
      buttons:         @broadcast&.message_template&.buttons || log_cache&.messages&.dig('buttons', 'template')
    }
  end

  def generate_text(contact, log)
    values = build_template_values(contact, log.contact_id)
    header = generate_text_header(log, values)
    body   = generate_text_body(log, values)
    footer = generate_text_footer(log, values)
    [header, body, footer].select(&:present?).join("\n\n")
  end

  def build_template_values(contact, contact_id)
    return @broadcast.contact_extra if @broadcast.is_direct? && @broadcast.contact_extra?

    extra = if @broadcast.contact_list_id.present?
      contact_extra = Models::ContactExtra.select(:extra).find_by(contact_list_id: @broadcast.contact_list_id, contact_id: contact_id)
      Hashie::Mash.new(contact_extra.nil? ? contact.extra : contact_extra.extra)
    else
      {}
    end

    extra.merge(contact.slice(:full_name, :phone_number))
  end

  def generate_text_header(log, values)
    if @broadcast.message_template.nil?
      template = log.messages&.dig('header', 'template', 'text')
      return if template.nil?
    else
      return if @broadcast.message_template.header.nil?
      template = @broadcast.message_template.header['text']
    end

    return if @broadcast.parameters['header']['format'] != 'TEXT'

    format_template template: template,
                    values:   values,
                    keymap:   @broadcast.parameters['header']['params']
  end

  def generate_text_body(log, values)
    if @broadcast.message_template.nil?
      template = log.messages&.dig('body', 'template')
      return if template.nil?
    else
      template = @broadcast.message_template.body
    end

    format_template template: template,
                    values:   values,
                    keymap:   @broadcast.parameters['body']
  end

  def generate_text_footer(log, values)
    if @broadcast.message_template.nil?
      template = log.messages&.dig('footer', 'template')
      return if template.nil?
    else
      template = @broadcast.message_template.footer
    end

    format_template template: template,
                    values:   values,
                    keymap:   @broadcast.parameters['footer']
  end

  # @example
  #   format_template template: 'Hello {{1}}',
  #                   values:   { 'full_name' => 'John Doe' },
  #                   keymap:   { '1' => 'full_name' }
  #   # => "Hello John Doe"
  def format_template(template:, values:, keymap:)
    values = keymap&.map { |num, key| ["{{#{num}}}", values[key]&.to_s&.strip] }.to_h
    template&.gsub(/\{\{\d+\}\}/.freeze, values)
  end

  def build_sender(message)
    message.sender
  end

  def parse_room_created_at(room_created_at)
    room_created_at.is_a?(String) ? Time.parse(room_created_at) : room_created_at
  end

  def update_message_reply_id(reply_id)
    @message.update(reply_id: reply_id)
  rescue => e
    handle_error(e)
  end

  def handle_interactive_log(contact, log_cache, message_id)
    return unless message_has_button? && valid_external_reply?(log_cache)
    update_message_reply_id(message_id)
  end

  def build_temp_attrs
    attrs = {
      message_broadcast_id: @broadcast.id,
      contact_id:           @contact.id,
      sender_name:          @contact.full_name,
      sender_phone_number:  @contact.phone_number
    }
    if message_has_button?
      attrs[:value] = @message.buttons.first['text']
    end
    attrs
  end

  def message_has_button?
    buttons = @message.buttons
    buttons&.any? && buttons&.first&.dig('type') == 'BUTTON'
  end

  def valid_external_reply?(log_cache)
    reply_id = @message&.raw_message&.dig('messages', -1, 'context', 'id')
    reply_id.present? && reply_id.eql?(log_cache.external_id)
  end

  def handle_saved_message(saved_message, log_cache)
    if saved_message.success?
      handle_interactive_log(@contact, log_cache, saved_message.success.id)
    else
      handle_error(saved_message.failure)
    end
  end
end
