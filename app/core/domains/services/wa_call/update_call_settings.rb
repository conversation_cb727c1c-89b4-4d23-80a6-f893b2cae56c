# frozen_string_literal: true

class Services::WaCall::UpdateCallSettings < Repositories::AbstractHttp
  include Dry::Monads::Do.for(:call)

  def initialize organization, status, recording: false, icon: 'DEFAULT'
    @organization = organization
    @status       = status
    @recording    = recording
    @icon         = icon
  end

  def call
    settings = {
      enabled:   @status,
      recording: @recording,
      icon:      @icon
    }

    if @status == false
      settings[:disabled_at] = Time.current.in_time_zone('Asia/Jakarta').strftime('%Y-%m-%d %H:%M:%S GMT%z')
    else
      settings[:enabled_at] = Time.current.in_time_zone('Asia/Jakarta').strftime('%Y-%m-%d %H:%M:%S GMT%z')
    end

    @organization.settings[:wa_call] = settings
    @organization.save

    channels = Models::ChannelIntegration.where(target_channel: 'wa_cloud', organization_id: @organization.id, is_active: true)
    channels.each do |channel|
      result = WaCall::Services::Apis.new(channel.webhook, @status).call
      Rollbar.error('Failed to update WA Call Settings', class: self.class, method: __method__, args: { phone_number_id: channel.webhook, settings: settings }) unless result.success?
    end

    settings
  end
end
