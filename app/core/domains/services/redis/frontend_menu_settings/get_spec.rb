# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Services::Redis::FrontendMenuSettings::Get do
  let(:call) { described_class.new(role).call }

  describe '#call' do
    let(:role) { 'agent' }

    context 'when redis is not set yet' do
      it 'return nil' do
        expect(call).to eq nil
      end
    end

    context 'when redis is set' do
      let(:json) {
        {
          homeUrl:    {
            url: 'inbox'
          },
          menu:       {
            inbox:    {
              title:    'Inbox',
              slug:     'inbox',
              status:   true,
              icon:     'inbox',
              redirect: '',
              category: 1
            },
            contacts: {
              title:    'Contacts',
              slug:     'contacts',
              status:   true,
              icon:     'contact',
              redirect: 'contacts',
              category: 1
            },
            login:    {
              url: 'login'
            }
          },
          submenu:    {
            inbox: {
              title:  'Inbox',
              slug:   'inbox',
              status: true,
              icon:   'las la-inbox'
            }
          },
          bottommenu: {
            profile_setting: {
              title:    'Profile Setting',
              slug:     'settings/accounts_management/users',
              status:   true,
              icon:     'profile',
              redirect: 'agents'
            }
          }
        }
      }

      before do
        Services::Redis::FrontendMenuSettings::Set.new(role, json.to_json).call
        Services::Redis::FrontendMenuSettings::Set.new('owner', json.to_json).call
      end

      it 'return json stringify' do
        expect(call).not_to eq nil
        expect(call).to eq json.to_json
      end

      it 'return json stringify with owner role' do
        expect(described_class.new('owner').call).to eq json.to_json
      end
    end
  end
end
