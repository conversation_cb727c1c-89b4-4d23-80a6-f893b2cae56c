# frozen_string_literal: true

module SupportTools
  module Services
    class FeatureFlag
      def initialize(company_id:)
        @organization = Models::Organization.find_by(company_id: company_id)
        raise StandardError, "Company ID #{company_id} is not valid" if @organization.blank?
      end

      def switch(feature:, value:)
        handler_method = "handle_#{feature}"
        if respond_to?(handler_method, true)
          send(handler_method, value)
        else
          raise ArgumentError, "Unknown feature: #{feature}"
        end

        # recache organization settings
        # organization.__elasticsearch__.index_document refresh: true
        ::Services::Redis::Organizations::Del.new(@organization.id).call
      end

      private

      def handle_wa_call(value)
        # make sure index available
        @organization.__elasticsearch__.index_document refresh: true
        ::Services::WaCall::UpdateCallSettings.new(@organization, value).call
      end

      def handle_wa_flow(value)
        @organization.with_lock do
          @organization.settings['enable_wa_flow_specific'] = value
          @organization.save!
        end
      end

      def handle_aaa_assign_new_room(value)
        @organization.with_lock do
          @organization.settings['auto_agent_allocation'] ||= {}
          @organization.settings['auto_agent_allocation']['assign_new_room'] = value
          @organization.save!
        end
      end
    end
  end
end
