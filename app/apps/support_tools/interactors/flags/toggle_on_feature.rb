# frozen_string_literal: true

# Interactor for support panel toggle on/off feature flag
class SupportTools::Interactors::Flags::ToggleOnFeature < Interactors::AbstractIteractor
  contract do
    params do
      required(:is_enable).filled(:bool)
      required(:flag).filled(:string)
      required(:company_id).filled(:string)
    end
  end

  include Dry::Monads[:result]
  include Dry::Monads::Do.for(:result)

  def result
    params = yield result_of_validating_params
    SupportTools::Services::FeatureFlag.new(company_id: params[:company_id]).switch(feature: params[:flag], value: params[:is_enable])

    Success(true)
  rescue => e
    Failure(e)
  end
end
