# frozen_string_literal: true

class MekariSso::Services::RevokeToken < Repositories::AbstractHttp
  include Dry::Monads::Do.for(:call)

  def initialize(token:)
    @uri = ENV['SSO_OAUTH2_URL']
    @token = token
  end

  def call
    request = if Services::Preference.new.enabled?(:enable_sso_circuit_breaker)
      yield call_request_circuit_breaker
    else
      call_request
    end

    parse_response(request)
  end

  private

  def call_request_circuit_breaker
    response = call_circuit
    return Failure '[Circuitbox] SSO server error' if response.nil?
    Success response
  end

  def call_request
    delete(path: @uri, body: nil, authorization: "Bearer #{@token}", timeout: ENV['SSO_REQUEST_TIMEOUT'].to_i)
  end

  def call_circuit
    sso_circuit_breaker.run(exception: false) do
      response = call_request

      # raise manually since typhoeus doesn't raise exception
      # exception must match with circuit exceptions config
      raise RequestTimeout if response&.timed_out?
      raise RequestError if [500, 502, 504, 503].include?(response.code.to_i)
      response
    end
  end
end
