# frozen_string_literal: true

RSpec.describe MekariSso::Services::RevokeToken do
  describe '#call' do
    context 'when given valid token' do
      before do
        stub_http_request(:delete, arguments: {
          path:          ENV['SSO_OAUTH2_URL'],
          body:          nil,
          authorization: "Bearer #{SsoStubber::ACCESS_TOKEN}",
          timeout:       ENV['SSO_REQUEST_TIMEOUT'].to_i
        })
        stub_http_response(Hashie::Mash.new({ code: 204, body: '' }))
      end

      it 'should return success' do
        response = described_class.new(token: SsoStubber::ACCESS_TOKEN).call
        expect(response).to be_success
        expect(response.success.code).to eq(204)
      end
    end

    context 'when given invalid token' do
      before do
        stub_http_request(:delete, arguments: {
          path:          ENV['SSO_OAUTH2_URL'],
          body:          nil,
          authorization: 'Bearer 123',
          timeout:       ENV['SSO_REQUEST_TIMEOUT'].to_i
        })
        stub_http_response(Hashie::Mash.new({ code: 404, body: { message: 'Not found' } }), response: :failure)
      end

      it 'should return failed' do
        response = described_class.new(token: '123').call
        expect(response).to be_failure
        expect(response.failure.body.message).to eq('Not found')
      end
    end

    context 'when request timed out and circuit breaker enabled' do
      before do
        Services::Preference.new.add :enable_sso_circuit_breaker, title: 'Flag for enable disable circuit breaker in sso api call', extra: {}, target: 'feature'
        Services::Preference.new.enable :enable_sso_circuit_breaker

        stub_request(:delete, ENV['SSO_OAUTH2_URL'])
          .with(
            body: nil,
          )
          .to_return(
            status:         0,
            body:           '',
            should_timeout: true
          )
      end

      it 'should return failed' do
        response = described_class.new(token: SsoStubber::ACCESS_TOKEN).call
        expect(response).to be_failure
        expect(response.failure).to eq('[Circuitbox] SSO server error')
      end
    end

    context 'when request server error and circuit breaker enabled' do
      before do
        Services::Preference.new.add :enable_sso_circuit_breaker, title: 'Flag for enable disable circuit breaker in sso api call', extra: {}, target: 'feature'
        Services::Preference.new.enable :enable_sso_circuit_breaker

        stub_request(:delete, ENV['SSO_OAUTH2_URL'])
          .with(
            body: nil,
            )
          .to_return(
            status: 500,
            body:   {
              message: 'server error'
            }.to_json
          )
      end

      it 'should return failed' do
        response = described_class.new(token: SsoStubber::ACCESS_TOKEN).call
        expect(response).to be_failure
        expect(response.failure).to eq('[Circuitbox] SSO server error')
      end
    end

    context 'when request success and circuit breaker enabled' do
      before do
        Services::Preference.new.add :enable_sso_circuit_breaker, title: 'Flag for enable disable circuit breaker in sso api call', extra: {}, target: 'feature'
        Services::Preference.new.enable :enable_sso_circuit_breaker

        stub_request(:delete, ENV['SSO_OAUTH2_URL'])
          .with(
            body: nil,
            )
          .to_return(
            status: 204,
            body:   ''
          )
      end

      it 'should return success' do
        response = described_class.new(token: SsoStubber::ACCESS_TOKEN).call
        expect(response).to be_success
        expect(response.success).to eq('')
      end
    end
  end
end
