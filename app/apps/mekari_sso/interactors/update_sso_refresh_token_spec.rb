# frozen_string_literal: true

require 'rails_helper'

RSpec.describe MekariSso::Interactors::UpdateSsoRefreshToken, preference: true, sso: true do
  describe '#result' do
    let(:user) { create :user_sso }
    let(:resource) { user.auth }
    let(:old_token) { create :token, resource_owner_id: resource.id, scopes: :web, revoked_at: nil }
    let(:chat_token) { SsoStubber::TOKEN_INFO_CODE.merge(created_at: Time.now, revoked_at: nil) }

    def interactor(params)
      interactor = described_class
      parameters = interactor.parameters(params.merge(ip_address: Faker::Internet.ip_v4_address, user_agent: Faker::Internet.user_agent))
      interactor.new(parameters)
    end

    before do
      Services::Preference.new.add :custom_access_token_expiry, title: 'flag to customize access token expiry', extra: { expires_in: 3600 }, target: 'feature'
      Services::Preference.new.enable :custom_access_token_expiry
    end

    context 'when missing token' do
      it 'should return error' do
        use_case = interactor({}).result
        expect(use_case).to be_failure
      end
    end

    context 'when user email is not found in Chat Panel' do
      before do
        stub_repository(MekariSso::Services::GetCurrentUser, Hashie::Mash.new(sso_user_response(params: { email: '<EMAIL>' })[:data][:attributes]))
      end

      it 'should return failure with message' do
        use_case = interactor({ token: SsoStubber::QONTAK_ONE_TOKEN_INFO_CODE[:access_token], refresh_token: SsoStubber::REFRESH_TOKEN, old_refresh_token: old_token.refresh_token, expires_in: 3600 }).result
        expect(use_case).to be_failure
        expect(use_case.failure).to eq('Auth not found')
      end
    end

    context 'when given valid params' do
      let(:revoke_instance) { instance_double(Repositories::Oauths::Revoke) }
      let(:new_access_token) { "#{user.id}_Pn7tMq3npajdb15M4VbNQ0Z07jGMf33a" }
      let(:new_refresh_token) { 'qReggs9aTqIXjPpy6rE7vN5FaPy7GWMZ' }

      before do
        stub_get_current_user_in_sso(token: new_access_token)
        allow(Repositories::Oauths::Revoke).to receive(:new).and_return(revoke_instance)
        allow(revoke_instance).to receive(:call).and_return(Dry::Monads::Success({}))
        @use_case = interactor({ token: new_access_token, refresh_token: new_refresh_token, old_refresh_token: old_token.refresh_token, expires_in: 3600 }).result
      end

      it 'should revoke the previous token' do
        expect(revoke_instance).to have_received(:call).once
        old_token.update(revoked_at: Time.now)
      end

      it 'should return chat access token based on refresh token' do
        expect(@use_case).to be_success
        expect(@use_case.success.class).to eq(Entities::Oauths::Token)
        expect(@use_case.success.created_at).to be_between(Time.zone.now.to_i - 10, Time.zone.now.to_i.to_i + 10)
        expect(@use_case.success).to have_attributes({
          access_token:  new_access_token,
          refresh_token: new_refresh_token,
          token_type:    SsoStubber::REFRESH_TOKEN_INFO[:token_type],
          expires_in:    SsoStubber::REFRESH_TOKEN_INFO[:expires_in]
        })

        expect(Models::DoorkeeperAccessToken.where(resource_owner_id: resource.id).count).to eq(2)
        expect(Models::DoorkeeperAccessToken.find_by(token: new_access_token).revoked_at).to be_nil
      end
    end
  end
end
