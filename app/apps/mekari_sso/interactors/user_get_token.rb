# frozen_string_literal: true

class MekariSso::Interactors::UserGetToken < Interactors::AbstractIteractor
  contract do
    params do
      required(:grant_type).filled(:string, included_in?: %w[authorization_code refresh_token])
      required(:user_agent).filled(:string)
      required(:ip_address).filled(:string)
      optional(:authorization_code).filled(:string)
      optional(:refresh_token).filled(:string)
      optional(:crm_refresh_token).filled(:string)
      optional(:crm_device_id).filled(:string)
    end
  end

  include Dry::Monads[:result]
  include Dry::Monads::Do.for(:result, :sync_organization_data)

  def result
    params = yield result_of_validating_params

    if Services::Preference.new.enabled?(:bypass_sso_oauth) && params[:grant_type] == 'refresh_token'
      previous_auth = yield MekariSso::Repositories::PreviousToken.new(refresh_token: params[:refresh_token]).call
      auth = Models::Auth.find_by(id: previous_auth.resource_owner_id)
      user = yield Repositories::User.new.find(auth.auth_able_id)

      Repositories::Oauths::Revoke.new(params: { client_id: ENV['CLIENT_ID'], client_secret: ENV['CLIENT_SECRET'], token: previous_auth.token }).call
      previous_auth.reload

      Repositories::Oauths::GenerateToken.new(user_id: user.id).call
    else
      auth_params = generate_auth_params(params: params.except(:user_agent, :ip_address, :crm_refresh_token))
      token_info = yield MekariSso::Services::Auth.new(auth_params).call
      user_profile = yield MekariSso::Services::GetCurrentUser.new(token: token_info.access_token).call
      resource = Repositories::Auths::FindByEmail.new(email: user_profile.email).call
      if resource.success?
        chat_user = resource.success
        super_admin = chat_user.scopes.include?(:super_admin) || chat_user.scopes.include?('super_admin')
        organization_id = super_admin ? nil : chat_user.auth_able.try(:organization_id)
        crm_token_attr = {}

        if params[:grant_type] == 'authorization_code'
          unless super_admin
            sync_user_data(user_id: chat_user.auth_able_id, sso_data: user_profile)
            sync_organization_from_sso(org_id: organization_id)
            send_data_to_moengage(user_id: chat_user.auth_able_id) if Services::Preference.new.enabled?(:send_data_to_moengage)
            send_last_login_to_launchpad(email: user_profile.email) if Services::Preference.new.enabled?(:send_last_session_to_launchpad)
            check_sso_company_mapping(token: token_info.access_token, user: chat_user.auth_able) if Services::Preference.new.enabled?(:check_sso_company_mapping_on_login)
            if seamless_auth_token_enabled?(user_profile.sso_id)
              crm_response = sync_crm_token(token_info, params[:crm_device_id])
              if crm_response.success?
                crm_token_attr['access_token'] = token_info.access_token
                crm_token_attr['refresh_token'] = token_info.refresh_token
                crm_token_attr['expires_in'] = token_info.expires_in
                crm_token_attr['crm_device_id'] = crm_response.success.device_id
              end
            end
          end

          MekariSso::Repositories::RecordLastSignInAt.new(auth_id: chat_user.id).call
        elsif params[:grant_type] == 'refresh_token'
          previous_auth = yield MekariSso::Repositories::PreviousToken.new(refresh_token: params[:refresh_token]).call
          Repositories::Oauths::Revoke.new(params: { client_id: ENV['CLIENT_ID'], client_secret: ENV['CLIENT_SECRET'], token: previous_auth.token }).call
          previous_auth.reload

          if seamless_refresh_token_enabled?(user_profile.sso_id) && params[:crm_refresh_token].present?
            crm_token = sync_crm_refresh_token(token_info, params[:refresh_token])
            if crm_token.success?
              crm_token_attr['access_token'] = token_info.access_token
              crm_token_attr['refresh_token'] = token_info.refresh_token
              crm_token_attr['expires_in'] = token_info.expires_in
            end
          end
        end

        MekariSso::Repositories::Token.new(token_info: token_info, resource_owner_id: resource&.success&.id, previous_auth: previous_auth, scopes: get_device_type(params[:user_agent]), ip_address: params[:ip_address], organization_id: organization_id, user_sso_id: user_profile.sso_id, crm_token_attr: crm_token_attr).call
      elsif Services::Preference.new.enabled?(:check_account_existence_on_login) && user_profile.email
        has_crm = Crm::Services::CheckEmailSecret.new(email: user_profile.email).call

        if Services::Preference.new.enabled?(:check_launchpad_params_on_login)
          launchpad_params = Launchpad::Services::GetLastSession.new(email: user_profile.email).call
          if launchpad_params.success?
            last_session = launchpad_params.success&.data&.last_session
            is_launchpad_superadmin = launchpad_params.success&.data&.is_superadmin

            token_info[:last_session] = last_session
            token_info[:is_launchpad_superadmin] = is_launchpad_superadmin
          end
        end

        Success token_info.merge(has_chat: false, has_crm: has_crm.success?, user_sso_id: user_profile.sso_id)
      else
        Failure resource.failure
      end
    end
  end

  private

  def generate_auth_params(params:)
    params.merge!({ app_name: app_name })
  end

  def app_name
    return MekariSso::Constants::AppName::QONTAK_ONE if Services::Preference.new.enabled? :unified_sso_login

    MekariSso::Constants::AppName::QONTAK_CHAT
  end

  def sync_user_data(user_id:, sso_data:)
    full_name = sso_data.last_name ? "#{sso_data.first_name} #{sso_data.last_name}" : sso_data.first_name
    Repositories::Users::Update.new(id: user_id, params: { full_name: full_name, phone: sso_data.phone, sso_synced_at: Time.zone.now }).call
  end

  def sync_organization_from_sso(org_id:)
    MekariSso::Workers::SyncOrganization.perform_async(org_id)
  end

  def send_data_to_moengage(user_id:)
    MoEngage::Workers::SendUserData.perform_async(user_id)
  end

  def send_last_login_to_launchpad(email:)
    Launchpad::Services::UpdateLastSession.new(email: email, last_session: 'chat').call
  end

  def check_sso_company_mapping(token:, user:)
    companies = MekariSso::Services::GetOwnedCompanies.new(token: token).call
    return if companies.failure?

    unless companies.success.with_indifferent_access['data'].any? { |item| item['id'] == user.organization.unified_sso_id }
      MekariSso::Services::AssignUserToOrganization.new(
        user_id:  user.id,
        org_id:   user.organization.id,
        app_name: app_name
      ).call
    end
  end

  def seamless_auth_token_enabled? sso_id
    Services::Preference.new.enabled?('seamless_chat_crm_cookies') || Services::PreferenceOrganization.new(:seamless_chat_crm_cookies_by_sso, organization_id: sso_id).enabled?
  end

  def sync_crm_token token_info, device_id
    Crm::Services::AuthorizeChatToken.new(token: token_info.access_token, refresh_token: token_info.refresh_token, valid_until: (Time.now + token_info.expires_in.to_i.seconds).strftime('%FT%T'), device_id: device_id).call
  end

  def seamless_refresh_token_enabled? sso_id
    Services::Preference.new.enabled?('seamless_chat_crm_refresh_token') || Services::PreferenceOrganization.new(:seamless_chat_crm_refresh_token_by_sso, organization_id: sso_id).enabled?
  end

  def sync_crm_refresh_token token_attr, old_refresh_token
    Crm::Services::RefreshSsoToken.new(token_attr: token_attr, old_refresh_token: old_refresh_token).call
  end
end
