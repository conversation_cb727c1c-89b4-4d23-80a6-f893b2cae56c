# frozen_string_literal: true

class MekariSso::Interactors::UpdateSsoRefreshToken < Interactors::AbstractIteractor
  contract do
    params do
      required(:token).filled(:string)
      required(:refresh_token).filled(:string)
      required(:old_refresh_token).filled(:string)
      required(:expires_in).filled(:integer)
      required(:user_agent).filled(:string)
      required(:ip_address).filled(:string)
    end
  end

  include Dry::Monads[:result]
  include Dry::Monads::Do.for(:result)

  def result
    params = yield result_of_validating_params

    user_profile = yield MekariSso::Services::GetCurrentUser.new(token: params[:token]).call
    resource = yield Repositories::Auths::FindByEmail.new(email: user_profile.email).call

    previous_token = yield MekariSso::Repositories::PreviousToken.new(refresh_token: params[:old_refresh_token]).call
    Repositories::Oauths::Revoke.new(params: { client_id: ENV['CLIENT_ID'], client_secret: ENV['CLIENT_SECRET'], token: previous_token.token }).call

    token_info = Hashie::Mash.new(access_token: params[:token], refresh_token: params[:refresh_token], expires_in: params[:expires_in])
    organization_id = resource.auth_able.try(:organization_id)
    MekariSso::Repositories::Token.new(token_info: token_info, resource_owner_id: resource&.id, scopes: get_device_type(params[:user_agent]), ip_address: params[:ip_address], organization_id: organization_id, enable_device_limit: false).call
  end
end
