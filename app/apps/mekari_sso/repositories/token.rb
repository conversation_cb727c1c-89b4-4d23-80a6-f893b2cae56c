# frozen_string_literal: true

class MekariSso::Repositories::Token < Repositories::AbstractRepository
  # token_info: object Hashie::Mash
  include Dry::Monads::Do.for(:call, :generate_auth)

  def initialize(resource_owner_id:, token_info:, previous_auth: nil, scopes: :web, ip_address: nil, organization_id: nil, user_sso_id: nil, crm_token_attr: nil, enable_device_limit: true)
    @resource_owner_id = resource_owner_id
    @token_info = token_info
    @previous_auth = previous_auth
    @scopes = scopes
    @ip_address = ip_address
    @organization_id = organization_id
    @user_sso_id = user_sso_id
    @crm_token_attr = crm_token_attr
    @enable_device_limit = enable_device_limit
  end

  def call
    params = prepare_params(owner_id: @resource_owner_id, token_info: @token_info, scopes: @scopes)
    auth = generate_auth(previous_auth: @previous_auth, params: params)
    Services::Redis::Auth::SetToken.new(token: auth.token, attr: auth.attributes).call
    if toggle_device_limit && @enable_device_limit
      # unless Services::Preference.new.enabled?(:excluded_device_limit, organization_id: @organization_id)
      LimitActiveDeviceService.perform_async(attr_for_limit_device(auth))
      # end
    end

    extra_params = {
      user_sso_id:    @user_sso_id,
      crm_token_attr: @crm_token_attr
    }

    if Services::Preference.new.enabled?(:check_account_existence_on_login)
      resource = Models::Auth.find_by(id: @resource_owner_id)
      has_crm = resource&.email ? Crm::Services::CheckEmailSecret.new(email: resource&.email).call.success? : false

      extra_params[:has_chat] = true
      extra_params[:has_crm] = has_crm

      if Services::Preference.new.enabled?(:check_launchpad_params_on_login)
        launchpad_params = Launchpad::Services::GetLastSession.new(email: resource.email).call
        if launchpad_params.success?
          extra_params[:last_session] = launchpad_params.success&.data&.last_session
          extra_params[:is_launchpad_superadmin] = launchpad_params.success&.data&.is_superadmin
        end
      end
    end

    Success Builders::Oauths::Token.new(auth, extra_params).build
  end

  private

  def toggle_device_limit
    if Services::Preference.new.enabled?('billing_move_flag_to_organization')
      organization = Models::Organization.find_by(id: @organization_id)
      return organization&.enable_device_limit || false rescue false
    end
    Services::Preference.new.enabled?(:enable_device_limit, organization_id: @organization_id)
  end

  def prepare_params(owner_id:, token_info:, scopes:)
    token_info.token = token_info.access_token
    token_info.merge(resource_owner_id: owner_id, revoked_at: nil, scopes: @scopes).except(:access_token, :token_type)
  end

  def generate_auth(previous_auth:, params:)
    auth = previous_auth
    params = params.merge(created_at: Time.now) if auth.present?
    auth ||= yield create_auth(params: params)

    # custom access token expiry
    if Services::Preference.new.enabled?(:custom_access_token_expiry)
      custom_expiry = Services::Preference.new.info(:custom_access_token_expiry).dig(:value)&.dig('expires_in')&.to_i
      expires_in = custom_expiry.zero? ? 3600 : custom_expiry
      params[:expires_in] = expires_in
    end

    auth.update(params.as_json)
    auth
  end

  def create_auth(params:)
    auth = Models::DoorkeeperAccessToken.new(params.as_json)
    return Failure error_messages_for auth unless auth.save
    Success auth
  end

  def attr_for_limit_device(auth)
    {
      auth:       auth.attributes,
      scopes:     @scopes,
      ip_address: @ip_address
    }
  end
end
