# frozen_string_literal: true

class WaCloud::Builders::Product < Builders::AbstractBuilder
  acts_as_builder_for_entity WaCloud::Entities::Product

  private

  def attributes_for_entity
    {
      description:  @ar_model_instance.description.presence || '-',
      image:        {
        image_ratio:     '1:1',
        image_url_lists: [@ar_model_instance.image_url.presence || '-']
      },
      item_id:      @ar_model_instance.id.presence || '-',
      item_name:    @ar_model_instance.name.presence || '-',
      price_info:   {
        currency:      @ar_model_instance.currency.presence || 'IDR',
        current_price: {
          value: clean_price(@ar_model_instance.price)
        },
        sale_price:    {
          value: clean_price(@ar_model_instance.sale_price)
        }
      },
      condition:    @ar_model_instance.condition.presence || '-',
      url:          @ar_model_instance.url.presence || '-',
      retailer_id:  @ar_model_instance.retailer_id.presence || '-',
      availability: @ar_model_instance.availability.presence || '-'
    }
  end

  def clean_price(price)
    price.to_s.gsub(/[^\d]/, '').presence || '0'
  end
end
