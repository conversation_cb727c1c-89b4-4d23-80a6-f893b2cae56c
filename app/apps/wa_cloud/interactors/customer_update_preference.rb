# frozen_string_literal: true

class WaCloud::Interactors::CustomerUpdatePreference < Interactors::AbstractIteractor
  contract do
    params do
      optional(:raw_message).maybe(:hash)
      required(:webhook).filled(:string)
      required(:account_uniq_id).filled(:string)
      required(:phone_number).filled(:string)
      required(:time).filled(:integer)
      required(:type).filled(:string)
      required(:text).filled(:string)
    end
  end

  include Dry::Monads[:result]
  include Dry::Monads::Do.for(:result)

  def result
    attrs = yield result_of_validating_params
    yield validate_app_type attrs[:webhook]

    organization_id = Services::ChannelIntegration::FetchOrganizationIdByWebhook.new(webhook: attrs[:webhook]).call
    return Failure 'organization id not found' if organization_id.nil?
    yield check_package_webhook(organization_id)

    check_blocked_contact = check_contact attrs[:account_uniq_id], organization_id
    return Failure yield check_blocked_contact if check_blocked_contact.success?
    if Services::Preference.new.enabled?(:check_blocked_global) || Services::Preference.new.enabled?(:check_blocked_global_per_org, organization_id: organization_id)
      is_blocked_global = check_blocked_global(attrs[:account_uniq_id], organization_id)
      return Failure yield is_blocked_global if is_blocked_global.success?
    end

    Services::Datadog::CaptureCustomMetric.new(name: :incoming_webhook_wa_cloud_message).capture

    # ke queue
    Publishers::WaCloudInboundMessage.new(attrs).publish
    Success Hashie::Mash.new({ status: 'success', params: attrs })
  end

  def check_contact account_uniq_id, organization_id
    Services::Contacts::IsBlockedContact.new(id: account_uniq_id, type: 'account_uniq_id', organization_id: organization_id).call
  end

  def validate_app_type webhook
    channel = Services::Redis::ChannelIntegrations::GetByWebhook.new(webhook).call
    return Failure 'Channel not found' if channel.nil?

    org = Services::Redis::Organizations::Get.new(channel.organization_id).call
    return Failure 'Organization not found' if org.nil?
    return Failure 'Organization is outbound only' if ['outbound'].include?(org.settings['app_type'])

    Success true
  end

  def check_blocked_global(account_uniq_id, organization_id)
    is_global_block_contact(account_uniq_id, 'wa_cloud', organization_id, 'account_uniq_id')
  end
end
