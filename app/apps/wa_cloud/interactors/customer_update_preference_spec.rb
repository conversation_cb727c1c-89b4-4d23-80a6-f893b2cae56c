# frozen_string_literal: true

require 'rails_helper'

RSpec.describe WaCloud::Interactors::CustomerUpdatePreference, switch_incoming_room_query: true, message: true do
  describe '#result' do
    include_context 'use case'

    subject { result }

    let(:organization) do
      org = create :org, :searchable,
                   settings:                        { billing_enabled: true },
                   agent_can_takeover:              true,
                   auto_responder_in_office_hours:  true,
                   response_message_in_office_hour: 'Welcome',
                   receive_message_from_agent:      true,
                   receive_message_from_customer:   true,
                   room_created:                    true
      create :bot_account
      org
    end
    let(:channel) { create :wa_cloud_channel, organization: organization }
    let(:contact) { create :default_contact, channel_integration: channel, account_uniq_id: '*************', full_name: 'Qontak customer' }

    before do
      allow_any_instance_of(Publishers::MessageSend).to receive(:publish)
      allow(Webhooks::RoomInteractionWorker).to receive(:perform_async)
      create :organization_package,
             organization: organization, wa_credit: 0, agent_credit: 0,
             muv_credit: 5, muv_extra: 5, status: 'active',
             valid_until: (DateTime.current + 2.months).to_i
      create :system_account

      Services::Preference.new.add :async_inbound_transaction, title: 'New workaround to optimize inbound', target: 'feature'
      Services::Preference.new.disable :async_inbound_transaction
      Services::Preference.new.add :force_recache_es_index_room_cache, title: 'Recache ES room cache index', extra: {}, target: 'feature'
      Services::Preference.new.enable :force_recache_es_index_room_cache
    end

    def build_message message
      Builders::Message.new(message).build
    end

    def get_cache
      Services::Redis::Organizations::UnassignedRoom.new(org_id: organization.id, ch_ids: [channel.id]).get
    end

    context 'with wa cloud text message when room does not exist' do
      let(:params) do
        {
          webhook:         channel.webhook,
          account_uniq_id: contact.account_uniq_id.to_phone,
          phone_number:    contact.account_uniq_id.to_phone,
          time:            Time.zone.now.to_i,
          type:            'system',
          text:            'User requested to stop marketing messages'
        }
      end

      it 'returns text message', :aggregate_failures do
        expect(result).to be_success
        expect(Models::Room.where(organization_id: organization.id, status: 'unassigned').count).to eq(1)
        expect(get_cache).to eq(1)
        expect(build_message(Models::Message.last)).to have_attributes(
                                                         type:           'system',
                                                         room_id:        an_instance_of(String),
                                                         sender_id:      contact.id,
                                                         sender_type:    contact.class.polymorphic_name,
                                                         participant_id: an_instance_of(String),
                                                         text:           params[:text],
                                                         status:         'created',
                                                         room:           have_attributes(
                                                                           session_at:       an_instance_of(ActiveSupport::TimeWithZone),
                                                                           last_message_at:  an_instance_of(ActiveSupport::TimeWithZone),
                                                                           last_activity_at: an_instance_of(ActiveSupport::TimeWithZone)
                                                         )
                                                       )
      end
    end

    context 'when given blocked global' do
      let(:params) do
        {
          webhook:         channel.webhook,
          account_uniq_id: contact.account_uniq_id.to_phone,
          phone_number:    contact.account_uniq_id.to_phone,
          time:            Time.zone.now.to_i,
          type:            'system',
          text:            'User requested to stop marketing messages'
        }
      end

      before do
        Services::Preference.new.add :check_blocked_global, title: 'This flag is check contact is blocked global or not', extra: {}, target: 'feature', author: '<EMAIL>'
        Services::Preference.new.enable :check_blocked_global
      end

      context 'when globally blocked' do
        before do
          stub_repository(Services::Contacts::IsBlockedGlobal, 'contact is blocked', arguments: { id: params[:account_uniq_id], channel: 'wa_cloud', organization_id: organization.id, id_type: 'account_uniq_id' })
        end

        it 'should return failure with valid message' do
          expect(result).to be_failure
          expect(result.failure).to eq('contact is blocked')
        end
      end

      context 'when not globally blocked' do
        before do
          stub_repository(Services::Contacts::IsBlockedGlobal, 'contact is not blocked', arguments: { id: params[:account_uniq_id], channel: 'wa_cloud', organization_id: organization.id, id_type: 'account_uniq_id' }, response: :failure)
        end

        it 'should return failure with valid message' do
          expect(result).to be_success
        end
      end
    end

    context 'when given blocked global per org' do
      let(:params) do
        {
          webhook:         channel.webhook,
          account_uniq_id: contact.account_uniq_id.to_phone,
          phone_number:    contact.account_uniq_id.to_phone,
          time:            Time.zone.now.to_i,
          type:            'system',
          text:            'User requested to stop marketing messages'
        }
      end

      before do
        Services::Preference.new.add :check_blocked_global_per_org, title: 'This flag is check contact is blocked global or not for org', extra: {}, target: 'feature', author: '<EMAIL>'
        Services::Preference.new.enable :check_blocked_global_per_org
        Services::Preference.new.set_organization_ids(:check_blocked_global_per_org, [organization.id])
        stub_repository(Services::Contacts::IsBlockedGlobal, 'contact is blocked', arguments: { id: params[:account_uniq_id], channel: 'wa_cloud', organization_id: organization.id, id_type: 'account_uniq_id' })
      end

      it 'should return failure with valid message' do
        expect(result).to be_failure
        expect(result.failure).to eq('contact is blocked')
      end
    end
  end
end
