# frozen_string_literal: true

class WaCloud::Repositories::Broadcast::Send < Repositories::AbstractHttp
  include Repositories::Billings::Helpers
  include Dry::Monads::Do.for(:call, :validate_balance, :validate_template)
  include Repositories::Whatsapp::Broadcasts::LogPartition
  include Repositories::Whatsapp::Broadcasts::Helpers

  MEDIA_BROADCAST = %w[IMAGE VIDEO DOCUMENT]
  private_constant :MEDIA_BROADCAST

  def initialize(params:)
    @params               = prepare! params
    @organization         = fetch_organization
    @phone_number_id      = @params.phone_number_id || Services::Redis::ChannelIntegrations::Get.new(@params.channel_integration_id).call.settings['phone_number_id']
    @message_template     = @params[:message_template] || Services::Redis::MessageTemplates::Get.new(@params.organization_id, @params.message_template_id).call
    @message_broadcast    = Models::MessageBroadcast.find_by(id: @params.messages_broadcast_id)
    @contact              = get_contact
    @broadcast_parameters = @params.messages_broadcast_parameters || @message_broadcast&.parameters
    if Services::Preference.new.enabled?(:broadcast_log_partition)
      filter = prepare_broadcast_log_part_date_range(message_broadcast_send_at: @message_broadcast.send_at)
      @log = Models::MessageBroadcastLog.find_by(messages_broadcast_id: @params.messages_broadcast_id, contact_phone_number: @params.contact_phone_number, created_at: filter[:start_date]..filter[:end_date])
    else
      @log = Models::MessageBroadcastLog.find_by(messages_broadcast_id: @params.messages_broadcast_id, contact_phone_number: @params.contact_phone_number)
    end
    @service              = WaCloud::Services::ApisAdapter.new(@organization.settings).call
    @billing              = @params[:billing_params] || build_broadcast_billing_params(@organization)
    @contact_extra        = @params.contact_extra || Services::Contacts::GetExtra.new(contact: @contact, contact_list_id: @message_broadcast&.contact_list&.id).call
  end

  def call
    yield validate_template

    ## Handle double message, Pastikan jika messages_broadcast_id dan contact_id sudah ada di log, langusng reject!!
    if @log.present? && @log.status != 'created'
      return failure(response: {}, messages: 'rejected')
    end

    ## Reject broadcast if balance not sufficient
    if @billing.present? && @billing[:enabled]
      balance_validation = validate_balance

      if balance_validation.failure?
        set_error_messages_for_broadcast(broadcast: @message_broadcast, error_messages: balance_validation.failure[:response]) if @message_broadcast
        return balance_validation
      end
    end

    message_params  = build_data
    phone_number_id = @phone_number_id

    conversation_category = @message_template&.category || 'marketing'
    conversation_category = conversation_category.downcase
    enable_marketing_message_lite = Services::Preference.new.enabled?(:enable_marketing_message_lite) && @organization.settings['waba_id'].present?
    if enable_marketing_message_lite
      waba_id = @organization.settings['waba_id']
      marketing_messages_lite_api_status_result = @service.get_marketing_messages_lite_api_status(waba_id)
      if marketing_messages_lite_api_status_result.failure?
        enable_marketing_message_lite = false
      else
        enable_marketing_message_lite = marketing_messages_lite_api_status_result.success['marketing_messages_lite_api_status'] == 'ONBOARDED'
      end
    end
    response = conversation_category == 'marketing' && enable_marketing_message_lite ? @service.send_marketing_message(phone_number_id, message_params) : @service.send_message(phone_number_id, message_params)
    if response.failure?
      return failure(response: response.failure, messages: build_response)
    end

    if conversation_category == 'marketing' && enable_marketing_message_lite
      @message_broadcast.is_mm_lite = true
      @message_broadcast.save
    end

    success(response: response.success, messages: build_response)
  end

  private

  def get_contact
    c = fetch_contact_unified(@params.contact_id, @params.contact_list_recipient_id)
    c || @params.contact || @message_broadcast&.contact_list&.contacts&.find_by(phone_number: @params.contact_phone_number) || @message_broadcast&.contact
  end

  def fetch_organization
    Services::Redis::Organizations::Get.new(@params.organization_id).call
  end

  def validate_template
    if @message_template.nil?
      return failure(response: Hashie::Mash.new({ internal_error: 'message template not found' }), messages: {})
    end

    success true
  end

  def validate_balance
    return billing_error('Unable to send broadcast because your account is currently in frozen mode') if @billing[:status].to_s.eql?('freeze')
    return billing_error('inactive_package') unless ['grace', 'active'].include?(@billing[:status])

    wa_package = Models::Billing::WhatsappPackage.find_by(waba_id: @billing[:waba_id])
    return success true unless wa_package.present?
    return billing_error('can not send message because still have minus balance') if wa_package.balance < 0

    conversation_category = @message_template&.category || 'marketing'
    conversation_category = conversation_category.downcase
    billing_service = Services::Billing::V2::WaPricing.new(@params.wa_id, 'BI', conversation_category) # all message from broadcast should categorized as Business Initiated conv
    balance_deduct = billing_service.total_price(@params.organization_id, @billing[:package_id])
    current_balance = wa_package.balance - balance_deduct
    current_balance_initial = wa_package.balance_initial - balance_deduct

    if @billing[:version].eql?('3.0.0') && @billing[:payment_type].eql?('postpaid')
      current_postpaid_limit = wa_package.postpaid_limit - balance_deduct
      return billing_error('insufficient_balance') if current_balance < 0 && current_balance_initial < 0 && current_postpaid_limit < 0
    elsif current_balance < 0 && current_balance_initial < 0
      return billing_error('insufficient_balance')
    end

    success true
  end

  def billing_error error_type
    failure(response: Hashie::Mash.new({ billing_error: error_type }), messages: build_response)
  end

  def set_error_messages_for_broadcast(broadcast:, error_messages:)
    broadcast.update(error_messages: error_messages)
  end

  def build_data
    {
      to:       @params.wa_id,
      type:     'template',
      template: {
        name:       @message_template.name,
        language:   { policy: 'deterministic', code: @message_template.language },
        components: build_components
      }
    }
  end

  def build_components
    components = []
    components.append(type: 'header', parameters: get_header_params) if get_header_params.present?
    components.append(type: 'body', parameters: get_body_params) if get_body_params.present?
    components + get_buttons_params
  end

  def get_header_params
    header_params = []
    return [] if @broadcast_parameters['header'].blank? || @contact.nil?

    # Currently we don't have a feature to handle broadcast with template header text
    # This condition will be adjust soon if the feature already developed
    return [] if @broadcast_parameters['header']['format'].eql?('TEXT')

    url_attachment = @broadcast_parameters.with_indifferent_access.dig(:header, :params, :url)
    return [] if url_attachment.nil? && MEDIA_BROADCAST.include?(@broadcast_parameters['header']['format'])
    uniq_url = "file_#{@params.channel_integration_id}_#{Digest::SHA1.hexdigest(url_attachment)}"
    media_id_cache = REDIS_R.get "Broadcast::#{uniq_url}::Media"
    type = nil

    case @broadcast_parameters['header']['format']
    when 'IMAGE'
      type = 'image'
      header_params.append(
        type:  type,
        image: {}
      )
    when 'VIDEO'
      type = 'video'
      header_params.append(
        type:  type,
        video: {}
      )
    when 'DOCUMENT'
      type = 'document'
      header_params.append(
        type:     type,
        document: {
          filename: @broadcast_parameters['header']['params']['filename']
        }
      )
    else
      type = 'text'
      contact_data = Hashie::Mash.new(@contact_extra)
      sorted_params = @broadcast_parameters['header']['params'].sort.to_h
      sorted_params.each do |_key, attr|
        header_params.append(
          type: type,
          text: contact_data[attr].to_s.strip
        )
      end
    end

    return header_params if type.eql?('text')

    if media_id_cache.present?
      header_params.last[type.to_sym][:id] = media_id_cache
    else
      header_params.last[type.to_sym][:link] = @broadcast_parameters['header']['params']['url']
    end

    header_params
  end

  def get_body_params
    return [] if @broadcast_parameters['body'].blank? || @contact.nil?
    contact_data              = Hashie::Mash.new(@contact_extra)
    contact_data.full_name    = @contact.full_name
    contact_data.phone_number = @contact.phone_number
    @broadcast_parameters['body'].sort_by { |key, value| key.to_i }.map! do |_key, attr|
      { type: 'text', text: contact_data[attr].to_s.strip }
    end
  end

  def get_buttons_params
    return [] if @broadcast_parameters['buttons'].blank?

    @broadcast_parameters['buttons'].sort_by { |key, value| key.to_i }.map! do |index, attr|
      parameters = attr['type'].eql?('quick_reply') ? [{ type: 'payload', payload: attr['value'] }] : [{ type: 'text', text: attr['value'] }]
      { type: 'button', sub_type: attr['type'], index: index, parameters: parameters }
    end
  end

  def build_response
    components = {}

    # header
    if @message_template.header.present? && get_header_params.present?
      type = @broadcast_parameters['header']['format']
      header = get_header_params
      header.last[type.downcase.to_sym][:link] = @broadcast_parameters['header']['params']['url']
      components['header'] = { type: 'header', parameters: header, template: @message_template.header }
    else
      components['header'] = { type: 'header', parameters: [], template: @message_template.header || {} }
    end

    # body
    if @message_template.body.present? && get_body_params.present?
      body = {}
      get_body_params.each_with_index.map { |el, index| body[index + 1] = el[:text] }
      components['body'] = { type: 'body', parameters: body, template: @message_template.body }
    else
      components['body'] = { type: 'body', parameters: [], template: @message_template.body }
    end

    # buttons
    if @message_template.buttons.present? && get_buttons_params.present?
      buttons = {}
      get_buttons_params.each_with_index.map { |el, index| buttons[index] = el[:parameters].last[:payload] }
      components['buttons'] = { type: 'buttons', parameters: buttons, template: @message_template.buttons }
    else
      components['buttons'] = { type: 'buttons', parameters: [], template: @message_template.buttons }
    end

    components
  end
end
