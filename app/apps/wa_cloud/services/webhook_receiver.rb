# frozen_string_literal: true

class WaCloud::Services::WebhookReceiver
  # https://developers.facebook.com/docs/whatsapp/cloud-api/webhooks
  def initialize(attr, save_log: true)
    @attr = Hashie::Mash.new(attr)
    @data, @webhook = fetch_data
    @save_log = save_log
  end

  def handle_message
    return {} if !@data.contacts? || !@data.messages?
    log_message if @save_log
    build_message_parameters
  end

  def handle_message_status
    log_status_message if @save_log
    { statuses: build_meta_error_response(@data.statuses.as_json), webhook: @webhook }
  end

  def handle_calls
    log_status_message if @save_log
    { calls: prepare_data_sdp(@data), webhook: @webhook }
  end

  def handle_user_preference
    return {} unless @data.user_preferences?
    log_message if @save_log
    build_message_user_preference_parameters
  end

  private

  def prepare_data_sdp data
    sdp = data.dig(:calls, 0, :session, :sdp)

    if sdp.present?
      unescape_sdp = sdp.gsub(/\\r/, "\r").gsub(/\\n/, "\n")
      data[:calls][0][:session][:sdp] = unescape_sdp
      data[:calls][0][:phone_number_id] = data.dig(:metadata, :phone_number_id)
    end
    data[:calls] || data[:statuses]
  end

  # [data, webhook]
  def fetch_data
    data = @attr.changes[0].value.whatsapp_business_api_data

    if data
      webhook = data.phone_number_id
    else
      data = @attr.changes[0].value
      metadata = data.metadata # when metadata inside value
      if metadata
        webhook = data.metadata.phone_number_id
      else
        webhook = @attr.changes[0].metadata.phone_number_id # when metadata one level with value
      end

    end

    [data, webhook]
  end

  def log_message
    account_uniq_id = @data.contacts[0].wa_id.to_phone
    Services::Logs::IncomingMessageLogsMapper.new(webhook: @webhook, attr: @attr, channel: :wa_cloud, account_uniq_id: account_uniq_id).call
  rescue => e
    Rollbar.error(e, class: self.class.name, method: 'call', args: { attr: @attr, webhook: @webhook })
  end

  def log_status_message
    Services::Logs::NotificationMessageLogsMapper.new(webhook: @webhook, attr: @data, type: 'wa_cloud').call
  rescue => e
    Rollbar.error(e, class: self.class.name, method: 'call', args: { attr: @attr, webhook: @webhook })
  end

  def build_message_parameters
    contact = @data.contacts[0]
    message = @data.messages[0]
    attr = {
      webhook:         @webhook,
      account_uniq_id: contact.wa_id.to_phone,
      name:            contact.profile.name,
      phone_number:    contact.wa_id.to_phone,
      external_id:     message.id,
      time:            message.timestamp.to_i,
      type:            message.type,
      raw_message:     @data.as_json,
      referral:        message.referral,
      group_id:        message.group_id
    }
    assets = build_assets_attributes message
    attr.merge(assets)
  end

  def build_message_user_preference_parameters
    contact = @data.contacts[0]
    message = @data.user_preferences[0]
    attr = {
      webhook:         @webhook,
      account_uniq_id: contact.wa_id,
      phone_number:    contact.wa_id,
      time:            message.timestamp.to_i,
      type:            'system',
      raw_message:     @data.as_json,
      text:            message.detail,
      external_id:     message.id
    }
    attr[:context] = message['context'] if message['context'].present?
    attr
  end

  def build_assets_attributes message
    media_type       = ['audio', 'contact', 'document', 'image', 'video', 'voice']
    text_type        = ['text']
    button_type      = ['button']
    interactive_type = ['interactive']
    location_type    = ['location']
    system_type      = ['system']
    order_type       = ['order']
    attr             = {}
    if media_type.include?(message.type)
      attr = handle_media_message message
    elsif text_type.include?(message.type)
      attr = handle_text_message message
    elsif button_type.include?(message.type)
      attr = handle_button_message message
    elsif interactive_type.include?(message.type)
      attr = handle_interactive_message message
    elsif location_type.include?(message.type)
      attr = handle_location_message message
    elsif system_type.include?(message.type)
      attr = handle_system_message message
    elsif order_type.include?(message.type)
      attr = handle_order_message message
    elsif message.errors?
      attr = handle_error_message message
    end
    attr[:context] = message['context'] if message['context'].present?
    attr
  end

  def handle_media_message message
    data = message[message.type]
    attr = {
      file:      data.id,
      file_type: data.mime_type
    }
    attr[:file_name] = data.filename if data.filename.present?
    attr[:text]      = data.caption if data.caption.present?
    attr
  end

  def handle_text_message message
    { text: message.text.body }
  end

  def handle_button_message message
    { text: message.button.text, type: 'text', buttons: [{ text: message.button.text, type: 'BUTTON' }] }
  end

  def handle_interactive_message message
    data = message[message.type]
    if data.type.eql?('list_reply')
      attr = { text: data['list_reply'].title, type: 'text' }
      attr[:text] += "\n#{data['list_reply'].description}" if data['list_reply'].description.present?
    elsif data.type.eql?('call_permission_reply')
      # TODO: Add call permission reply
      # https://developers.facebook.com/community/threads/465733769756616/
      if data.dig(:call_permission_reply, :response) == 'accept'
        attr = { text: 'Allow', type: 'text' }
      else
        attr = { text: "Don't Allow", type: 'text' }
      end
    elsif data.type.eql?('nfm_reply') && data.nfm_reply.present? && data.nfm_reply.name.eql?('flow')
      attr = { text: '✅ Data submitted', form_response: data.nfm_reply.response_json, type: 'text' }
    else
      attr = { text: data['button_reply'].title, type: 'text' }
      attr[:buttons] = [{ id: data['button_reply'].id, text: attr[:text], type: 'BUTTON_REPLY' }]
    end
    attr
  end

  def handle_location_message message
    data = message[message.type]
    text = 'No location Info'
    text = (data.address rescue nil) || text
    text = (data.name rescue nil) || text unless text.present?
    text = text unless text.present?
    { text: text }
  end

  def handle_error_message message
    error_message = message.errors[0].details
    { text: error_message, type: 'text' }
  end

  def handle_system_message message
    system_message = message.system.body
    { text: system_message, type: 'text' }
  end

  def handle_order_message message
    { text: 'Order message not supported', type: 'text' }
  end

  def build_meta_error_response(message_status)
    code_response = message_status&.first.try(:[], 'errors')&.first.try(:[], 'code')
    if code_response.present? && Constants::Meta::ErrorCodes::STATUSES[code_response].present?
      message_status&.first.try(:[], 'errors')[0]['title'] = Constants::Meta::ErrorCodes::STATUSES[code_response]
    end
    message_status
  end
end
