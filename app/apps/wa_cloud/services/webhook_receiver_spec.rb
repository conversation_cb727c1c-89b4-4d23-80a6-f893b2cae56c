# frozen_string_literal: true

RSpec.describe WaCloud::Services::WebhookReceiver, message: true do
  describe '#result' do
    subject(:message_handler_result) { described_class.new(message_attrs).handle_message }
    subject(:v17_message_handler_result) { described_class.new(v17_message_attrs).handle_message }
    subject(:message_status_handler_result) { described_class.new(status_attrs).handle_message_status }
    subject(:v17_message_status_handler_result) { described_class.new(v17_status_attrs).handle_message_status }
    subject(:call_permission_reply_handler_result) { described_class.new(call_permission_reply_message_attrs).handle_message }
    subject(:order_handler_result) { described_class.new(order_message_attrs).handle_message }

    let(:call_permission_reply_message_attrs) do
      {
        "id":      '535542020887309',
        "changes": [
          {
            "value": {
              "messaging_product": 'whatsapp',
              "metadata":          {
                "display_phone_number": '628100000000',
                "phone_number_id":      '20768100000000'
              },
              "contacts":          [
                {
                  "profile": {
                    "name": '<PERSON><PERSON>'
                  },
                  "wa_id":   '************'
                }
              ],
              "messages":          [
                {
                  "from":        '************',
                  "id":          'wamid.HBgNNjI4ODIxMjEyMzIyNRUCABIYIDIxNzY2M0U3MjE3NTA1QTg5OEY5QUVGOTNDMjgAMkREAA==',
                  "timestamp":   '**********',
                  "type":        'interactive',
                  "interactive": {
                    "type":                  'call_permission_reply',
                    "call_permission_reply": {
                      "response":             'accept',
                      "expiration_timestamp": ''
                    }
                  }
                }
              ]
            },
            "field": 'messages'
          }
        ]
      }
    end

    let(:message_attrs) do
      {
        "id":      '***************',
        "changes": [
          {
            "field": 'messages',
            "value": {
              "whatsapp_business_api_data": {
                "display_phone_number": '************',
                "phone_number_id":      '***************',
                "contacts":             [contact_payload],
                "messages":             [message_payload]
              }
            }
          }
        ]
      }
    end

    let(:v17_message_attrs) do
      {
        "id":      '105481682333521',
        "changes": [
          {
            "field": 'messages',
            "value": {
              "messaging_product": 'whatsapp',
              "metadata":          {
                "display_phone_number": '***********',
                "phone_number_id":      '123456123'
              },
              "contacts":          [contact_payload],
              "messages":          [message_payload]
            }
          }
        ]
      }
    end

    let(:status_attrs) do
      {
        "id":      '***************',
        "changes": [
          {
            "field": 'messages',
            "value": {
              "whatsapp_business_api_data": {
                "display_phone_number": '************',
                "phone_number_id":      '***************',
                "statuses":             [status_payload]
              }
            }
          }
        ]
      }
    end

    let(:v17_status_attrs) do
      {
        "id":      'WHATSAPP_BUSINESS_ACCOUNT_ID',
        "changes": [
          {
            "value": {
              "messaging_product": 'whatsapp',
              "metadata":          {
                "display_phone_number": '************',
                "phone_number_id":      '***************'
              },
              "statuses":          [status_payload]
            },
            "field": 'messages'
          }
        ]
      }
    end

    let(:order_message_attrs) do
      {
        "id":      '***************',
        "changes": [
          {
            "field": 'messages',
            "value": {
              "contacts":          [contact_payload],
              "messages":          [message_payload],
              "metadata":          {
                "phone_number_id":      '***************',
                "display_phone_number": '*************'
              },
              "messaging_product": 'whatsapp'
            }
          }
        ]
      }
    end

    context 'when message type is text' do
      let(:contact_payload) do
        {
          "profile": { "name": 'Hasta' },
          "wa_id":   '*************'
        }
      end

      let(:message_payload) do
        {
          "from":      '*************',
          "id":        'wamid.************************************************************',
          "timestamp": '**********',
          "text":      {
            "body": 'test'
          },
          "type":      'text'
        }
      end

      it 'successfully build text payload', :aggregate_failures do
        expect(message_handler_result).to include(
                                            webhook:         message_attrs[:changes][0][:value][:whatsapp_business_api_data][:phone_number_id],
                                            account_uniq_id: contact_payload[:wa_id],
                                            name:            contact_payload[:profile][:name],
                                            phone_number:    contact_payload[:wa_id],
                                            external_id:     message_payload[:id],
                                            time:            message_payload[:timestamp].to_i,
                                            type:            'text',
                                            text:            message_payload[:text][:body]
                                          )
        expect(Models::IncomingMessageLog.wa_cloud.count).to eql 1
      end
    end

    context 'when message type is media' do
      let(:contact_payload) do
        {
          "profile": { "name": 'Hasta' },
          "wa_id":   '*************'
        }
      end

      let(:message_payload) do
        {
          "from":      '*************',
          "id":        'wamid.************************************************************',
          "timestamp": '**********',
          "image":     {
            "id":        'IMAGE-ID',
            "sha256":    'IMAGE-SHA',
            "mime_type": 'image/jpeg'
          },
          "type":      'image'
        }
      end

      it 'successfully build media payload', :aggregate_failures do
        expect(message_handler_result).to include(
                                            webhook:         message_attrs[:changes][0][:value][:whatsapp_business_api_data][:phone_number_id],
                                            account_uniq_id: contact_payload[:wa_id],
                                            name:            contact_payload[:profile][:name],
                                            phone_number:    contact_payload[:wa_id],
                                            external_id:     message_payload[:id],
                                            time:            message_payload[:timestamp].to_i,
                                            type:            'image',
                                            file:            message_payload[:image][:id],
                                            file_type:       message_payload[:image][:mime_type]
                                          )
        expect(Models::IncomingMessageLog.wa_cloud.count).to eql 1
      end
    end

    context 'when message type is status' do
      let(:status_payload) do
        {
          "id":           'wamid.************************************************************',
          "status":       'delivered',
          "timestamp":    '1638506565',
          "recipient_id": '*************',
          "conversation": { "id": 'CONVERSATION-ID' },
          "pricing":      {
            "billable":      true,
            "pricing_model": 'CBP'
          }
        }
      end

      it 'successfully build status payload', :aggregate_failures do
        expect(message_status_handler_result).to include(
                                                   webhook:  status_attrs[:changes][0][:value][:whatsapp_business_api_data][:phone_number_id],
                                                   statuses: status_attrs[:changes][0][:value][:whatsapp_business_api_data][:statuses].as_json
                                          )
        expect(Models::NotificationMessageLog.count).to eql 1
      end
    end

    context 'when message type is failed status' do
      let(:status_payload) do
        {
          "id":           'wamid.HBgNNjI4NTM1NTk3MDAwMRUCABEYEjE5OENBMDZDMENFQUExRjdENgA=',
          "errors":       [
            {
              "code":  130472,
              "href":  'https://developers.facebook.com/docs/whatsapp/cloud-api/support/error-codes/',
              "title": "Failed to send message because this user's phone number is part of an experiment"
            }
          ],
          "status":       'failed',
          "timestamp":    '1690349321',
          "recipient_id": '*************'
        }
      end

      let(:status_response) do
        {
          "statuses": [
            {
              'id'           => 'wamid.HBgNNjI4NTM1NTk3MDAwMRUCABEYEjE5OENBMDZDMENFQUExRjdENgA=',
              'errors'       => [
                {
                  'code'  => 130472,
                  'href'  => 'https://developers.facebook.com/docs/whatsapp/cloud-api/support/error-codes/',
                  'title' => 'Failed to send marketing broadcast due to Meta/WhatsApp experimentation. Please send broadcast with another category.'
                }
              ],
              'status'       => 'failed',
              'timestamp'    => '1690349321',
              'recipient_id' => '*************'
            }
          ],
          "webhook":  '***************'
        }
      end

      it 'successfully build status payload', :aggregate_failures do
        expect(message_status_handler_result).to include(
                                                   webhook:  status_attrs[:changes][0][:value][:whatsapp_business_api_data][:phone_number_id],
                                                   statuses: status_response[:statuses].as_json
                                          )
        expect(Models::NotificationMessageLog.count).to eql 1
      end
    end

    context 'when message type is system but not valid' do
      let(:message_attrs) do
        {
          'id'      => '101801602603710',
          'changes' => [{
            'value' => {
              'whatsapp_business_api_data' => {
                'display_phone_number' => '6285299999721',
                'phone_number_id' => '106511742148504', 'messages' => [{
                  'from'      => '6285256322944',
                  'id'        => 'wamid.HBgNNjI4NTI1NjMyMjk0NBUCABIYEjM2Q0QzNzcyQjEwNEE2OTk0MQA=',
                  'timestamp' => '1677859539',
                  'system'    => {
                    'body'      => 'User A changed from 6285256322944 to 62882021770290',
                    'new_wa_id' => '62882021770290',
                    'type'      => 'user_changed_number'
                  },
                  'type'      => 'system'
                }]
              }
            },
            'field' => 'messages'
          }]
        }
      end

      it 'successfully response empty hash', :aggregate_failures do
        expect(message_handler_result).to eq({})
        expect(Models::IncomingMessageLog.count).to eql 0
      end
    end

    describe 'v17 webhook' do
      context 'when message type is text' do
        let(:contact_payload) do
          {
            "profile": { "name": 'Name' },
            "wa_id":   '*************'
          }
        end

        let(:message_payload) do
          {
            "from":      '*************',
            "id":        'wamid.************************************************************',
            "timestamp": '**********',
            "text":      {
              "body": 'test'
            },
            "type":      'text'
          }
        end

        it 'successfully build text payload', :aggregate_failures do
          expect(v17_message_handler_result).to include(
                                                  webhook:         v17_message_attrs[:changes][0][:value][:metadata][:phone_number_id],
                                                  account_uniq_id: contact_payload[:wa_id],
                                                  name:            contact_payload[:profile][:name],
                                                  phone_number:    contact_payload[:wa_id],
                                                  external_id:     message_payload[:id],
                                                  time:            message_payload[:timestamp].to_i,
                                                  type:            'text',
                                                  text:            message_payload[:text][:body]
                                            )
          expect(Models::IncomingMessageLog.wa_cloud.count).to eql 1
        end
      end

      context 'when message type is media' do
        let(:contact_payload) do
          {
            "profile": { "name": 'Name' },
            "wa_id":   '*************'
          }
        end

        let(:message_payload) do
          {
            "from":      '*************',
            "id":        'wamid.ID',
            "timestamp": '**********',
            "type":      'image',
            "image":     {
              "caption":   'CAPTION',
              "mime_type": 'image/jpeg',
              "sha256":    'IMAGE_HASH',
              "id":        'ID'
            }
          }
        end

        it 'successfully build media payload', :aggregate_failures do
          expect(v17_message_handler_result).to include(
                                                  webhook:         v17_message_attrs[:changes][0][:value][:metadata][:phone_number_id],
                                                  account_uniq_id: contact_payload[:wa_id],
                                                  name:            contact_payload[:profile][:name],
                                                  phone_number:    contact_payload[:wa_id],
                                                  external_id:     message_payload[:id],
                                                  time:            message_payload[:timestamp].to_i,
                                                  type:            'image',
                                                  file:            message_payload[:image][:id],
                                                  file_type:       message_payload[:image][:mime_type]
                                            )
          expect(Models::IncomingMessageLog.wa_cloud.count).to eql 1
        end
      end

      context 'when status message is delivered' do
        let(:status_payload) do
          {
            "id":           'wamid.************************************************************',
            "status":       'sent',
            "timestamp":    '1638506565',
            "recipient_id": '*************',
            "conversation": {
              "id":                   'CONVERSATION_ID',
              "expiration_timestamp": 'CONVERSATION_EXPIRATION_TIMESTAMP',
              "origin":               {
                "type": 'user_initiated'
              }
            },
            "pricing":      {
              "billable":      true,
              "pricing_model": 'CBP',
              "category":      'user_initiated'
            }
          }
        end

        it 'successfully build status payload', :aggregate_failures do
          expect(v17_message_status_handler_result).to include(
                                                         webhook:  v17_status_attrs[:changes][0][:value][:metadata][:phone_number_id],
                                                         statuses: v17_status_attrs[:changes][0][:value][:statuses].as_json
                                            )
          expect(Models::NotificationMessageLog.count).to eql 1
        end
      end

      context 'when message type is media and have referral object' do
        let(:contact_payload) do
          {
            "profile": { "name": 'Hasta' },
            "wa_id":   '*************'
          }
        end

        let(:message_payload) do
          {
            "from":      '*************',
            "id":        'wamid.************************************************************',
            "timestamp": '**********',
            "image":     {
              "id":        'IMAGE-ID',
              "sha256":    'IMAGE-SHA',
              "mime_type": 'image/jpeg'
            },
            "referral":  {
              "source_url": 'url',
              "ctwa_clid":  SecureRandom.uuid
            },
            "type":      'image'
          }
        end

        it 'successfully build media payload', :aggregate_failures do
          expect(message_handler_result).to include(
                                              webhook:         message_attrs[:changes][0][:value][:whatsapp_business_api_data][:phone_number_id],
                                              account_uniq_id: contact_payload[:wa_id],
                                              name:            contact_payload[:profile][:name],
                                              phone_number:    contact_payload[:wa_id],
                                              external_id:     message_payload[:id],
                                              time:            message_payload[:timestamp].to_i,
                                              type:            'image',
                                              file:            message_payload[:image][:id],
                                              file_type:       message_payload[:image][:mime_type],
                                              referral:        message_payload[:referral]
                                            )
          expect(Models::IncomingMessageLog.wa_cloud.count).to eql 1
        end
      end

      context 'when message have group_id' do
        let(:contact_payload) do
          {
            "profile": { "name": 'Hasta' },
            "wa_id":   '*************'
          }
        end

        let(:message_payload) do
          {
            "from":      '*************',
            "id":        'wamid.************************************************************',
            "group_id":  '**************************************************************',
            "timestamp": '**********',
            "text":      {
              "body": 'test'
            },
            "type":      'text'
          }
        end

        it 'successfully build media payload', :aggregate_failures do
          expect(v17_message_handler_result).to include(
                                                  webhook:         v17_message_attrs[:changes][0][:value][:metadata][:phone_number_id],
                                                  account_uniq_id: contact_payload[:wa_id],
                                                  name:            contact_payload[:profile][:name],
                                                  phone_number:    contact_payload[:wa_id],
                                                  external_id:     message_payload[:id],
                                                  time:            message_payload[:timestamp].to_i,
                                                  type:            'text',
                                                  text:            message_payload[:text][:body],
                                                  group_id:        message_payload[:group_id]
          )
          expect(Models::IncomingMessageLog.wa_cloud.count).to eql 1
        end
      end
    end

    describe 'when message type is interactive call_permission_reply' do
      let(:contact_payload) do
        {
          "profile": { "name": 'Kila' },
          "wa_id":   '************'
        }
      end

      let(:message_payload) do
        {
          "from":        '************',
          "id":          'wamid.HBgNNjI4ODIxMjEyMzIyNRUCABIYIDIxNzY2M0U3MjE3NTA1QTg5OEY5QUVGOTNDMjgAMkREAA==',
          "timestamp":   '**********',
          "type":        'interactive',
          "interactive": {
            "type": 'call_permission_reply'
          }
        }
      end

      it 'response not supported message', :aggregate_failures do
        expect(call_permission_reply_handler_result).to include(
                                                          webhook:         call_permission_reply_message_attrs[:changes][0][:value][:metadata][:phone_number_id],
                                                          account_uniq_id: contact_payload[:wa_id],
                                                          name:            contact_payload[:profile][:name],
                                                          phone_number:    contact_payload[:wa_id],
                                                          external_id:     message_payload[:id],
                                                          time:            message_payload[:timestamp].to_i,
                                                          type:            'text',
                                                          text:            'Allow'
        )
        expect(Models::IncomingMessageLog.count).to eql 1
      end
    end

    describe 'when message type is flow' do
      let(:contact_payload) {
        {
          profile: { name: 'Name' },
          wa_id:   '*************'
        }
      }

      let(:message_payload) {
        {
          context:     {
            from: '*************',
            id:   'wamid.HBgNNjI4MTMyMTA2MDA1OBUCABEYEkIwQ0U5QTk2ODdGMkNBMEI2NwA='
          },
          from:        '6281321060058',
          id:          'wamid.HBgNNjI4MTMyMTA2MDA1OBUCABIYFDNBMEE2NjdFNjJBRERERjlDRENCAA==',
          timestamp:   '1738738221',
          type:        'interactive',
          interactive: {
            type:      'nfm_reply',
            nfm_reply: {
              response_json: '{"Mie favorite":"sedap","Foto laptop":[{"id":2358980924483990,"mime_type":"image\\/jpeg","sha256":"jw5LcDeiBXojBKQTlf6Y5gQloj8poTh6qEuxuniXtvc=","file_name":"0016C5D0-0E48-465B-B339-527C57294481.jpg"},{"id":2686906151494894,"mime_type":"image\\/jpeg","sha256":"PSK5XW9P4vZQueWrSM9PJBUdBJyQckOyrdeprZKEIVk=","file_name":"DB11F6CC-AD5D-4682-9A18-DD2CE35A971D.jpg"},{"id":979336533525961,"mime_type":"image\\/jpeg","sha256":"fEfcvfwK4TlFqSDyWh33YaVKHg7Hd0ucUl2IbXOX+wI=","file_name":"85BEF02D-7E06-4A83-81F2-1F226EF46705.jpg"},{"id":612490998041289,"mime_type":"image\\/jpeg","sha256":"oKj1lx3n1vbxLRG1PHkq1FaaXzmoFgorFI64tbGAnX4=","file_name":"4AC69527-DAEE-4B48-8EC2-64F468515810.jpg"}],"Apa yang akan anda lakukan berikutnya":["0_Buy_it_right_away","2_Share_it_with_friends_family"],"Tanggal ulang tahun":"1993-02-05","Nama":"jajud","Jelaskan diri Anda":"saya tampan","Bahasa favorite":["ruby","go"],"flow_token":"eyJhbGciOiJIUzI1NiJ9.eyJmbG93X2lkIjoiNjAzOTQyMzcyMjgxNzkzIiwiZmxvd19uYW1lIjoic2Vtb2dhX2Zsb3dfbGVuZ2thcCIsImV4cGlyZWRfYXQiOjE3Mzg4MjQ1MjB9.4c5UgbPMjBrkOQW9QIXy7OMsMd6bKufAOKKohO9pjHg","Tempate favorite":["hotel","home"],"Tanggal pertama bekerja":"2025-02-03","Apakah ini option":true,"Role":"backend","Apa persiapan untuk ulang tahun":"1_Wear_the_same_as_usual","Best gift for a friend":["0_A_gift_voucher","2_A_boquet_of_flowers"]}',
              body:          'Sent',
              name:          'flow'
            }
          }
        }
      }

      it 'successfully build' do
        expect(v17_message_handler_result).to include(
                                                text:          '✅ Data submitted',
                                                type:          'text',
                                                form_response: message_payload[:interactive][:nfm_reply][:response_json],
                                                context:       message_payload[:context]
        )
      end
    end

    describe 'when message type is order' do
      let(:contact_payload) do
        {
          "wa_id":   '6281234995661',
          "profile": {
            "name": 'hilmi dama'
          }
        }
      end

      let(:message_payload) do
        {
          "id":        'wamid.HBgNNjI4MTIzNDk5NTY2MRUCABIYIDM2RDdFNkEzRkU0RjREMjJGRTcwRTI2MDEyQjA2MEE0AA==',
          "from":      '6281234995661',
          "type":      'order',
          "order":     {
            "text":          '',
            "catalog_id":    '677688895178391',
            "product_items": [
              {
                "currency":            'IDR',
                "quantity":            1,
                "item_price":          150000,
                "product_retailer_id": 'g3fm47nsoo'
              }
            ]
          },
          "timestamp": '**********'
        }
      end

      it 'response not supported message', :aggregate_failures do
        expect(order_handler_result).to include(
                                          webhook:         order_message_attrs[:changes][0][:value][:metadata][:phone_number_id],
                                          account_uniq_id: contact_payload[:wa_id],
                                          name:            contact_payload[:profile][:name],
                                          phone_number:    contact_payload[:wa_id],
                                          external_id:     message_payload[:id],
                                          time:            message_payload[:timestamp].to_i,
                                          type:            'text',
                                          text:            'Order message not supported'
        )
        expect(Models::IncomingMessageLog.count).to eql 1
      end
    end

    context 'when message from user preference' do
      subject(:message_handle_user_preference_result) { described_class.new(user_preferences_attrs).handle_user_preference }
      let(:user_preferences_contatct) do
        {
          "wa_id": '*************'
        }
      end

      let(:user_preferences_payload) do
        {
          "category":  'marketing_messages',
          "detail":    'User requested to stop marketing messages',
          "timestamp": 1751878454,
          "value":     'stop',
          "wa_id":     '*************'
        }
      end

      let(:user_preferences_attrs) do
        {
          "id":      '***************',
          "changes": [
            {
              "field": 'user_preferences',
              "value": {
                "contacts":          [user_preferences_contatct],
                "messaging_product": 'whatsapp',
                "metadata":          {
                  "display_phone_number": '*************',
                  "phone_number_id":      '***************'
                },
                "user_preferences":  [user_preferences_payload]
              }
            }
          ]
        }
      end

      it 'successfully build text payload', :aggregate_failures do
        expect(message_handle_user_preference_result).to include(
                                                           webhook:         user_preferences_attrs[:changes][0][:value][:metadata][:phone_number_id],
                                                           account_uniq_id: user_preferences_contatct[:wa_id],
                                                           phone_number:    user_preferences_contatct[:wa_id],
                                                           time:            user_preferences_payload[:timestamp].to_i,
                                                           type:            'system',
                                                           text:            user_preferences_payload[:detail]
                                          )
        expect(Models::IncomingMessageLog.wa_cloud.count).to eql 1
      end
    end
  end
end
