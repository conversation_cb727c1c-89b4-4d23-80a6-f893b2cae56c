# frozen_string_literal: true

class Meta::Interactors::ReceiveNotificationAccountUpdate < Interactors::AbstractIteractor
  include Dry::Monads[:result]
  include Dry::Monads::Do.for(:result)

  PARTNER_REMOVED = 'PARTNER_REMOVED'
  public_constant :PARTNER_REMOVED
  AD_ACCOUNT_LINKED = 'AD_ACCOUNT_LINKED'
  public_constant :AD_ACCOUNT_LINKED

  contract do
    params do
      required(:valid).filled(:bool)

      optional(:meta_event_type).filled(:string, included_in?: [PARTNER_REMOVED, AD_ACCOUNT_LINKED])
      optional(:waba_id).maybe(:string)
      optional(:ad_account_id).maybe(:string)
      optional(:owner_business_id).maybe(:string)
      optional(:phone_number).maybe(:string)
    end
  end

  def result
    params = yield result_of_validating_params
    return Success true if params[:valid].eql? false

    case params[:meta_event_type]
    when PARTNER_REMOVED
      return Success true unless Services::Preference.new.enabled?(:enable_auto_remove_waba_channel)
      organization = Models::Organization.where("settings ->> 'waba_id' = ?", params[:waba_id]).first
      if organization.present?
        when_partner_removed(organization, params)
      end
    when AD_ACCOUNT_LINKED
      when_ad_account_linked(params)
    end
    Success true
  end

  private

  def when_partner_removed(organization, params)
    old_settings = organization.settings
    organization.settings = old_settings.except('waba_id', 'waba_name', 'waba_message_template_namespace')

    channels = organization.channel_integrations.wa_cloud
    if channels.present?
      channels.each do |c|
        if c.settings['type'].eql?('coexistence')
          c.is_active = false
          c.is_auto_resolve = false
          c.save

          delete_channel_cache(c)
          c.try(:destroy)
        end
      end
    end

    organization.save
    del_org_cache(organization)
    update_channel_division(organization.id)
    remove_old_sync_message_coexistence(organization)
  end

  def when_ad_account_linked(params)
    organizations = Models::Organization.where("settings ->> 'waba_id' = ?", params[:waba_id])
    unless organizations.blank?
      organizations.each do |organization|
        @service = WaCloud::Services::ApisAdapter.new(organization.settings).call
        marketing_messages_lite_api_status_result = @service.get_marketing_messages_lite_api_status(params[:waba_id])
        if marketing_messages_lite_api_status_result.failure?
          next
        else
          organization.settings[:ad_account_id] = params[:ad_account_id]
          organization.settings[:owner_business_id] = params[:owner_business_id]
          organization.settings[:mm_lite_api_status] = marketing_messages_lite_api_status_result.success['marketing_messages_lite_api_status']

          organization.settings[:mm_lite_onboarded_at] = Time.current.in_time_zone('Asia/Jakarta').strftime('%Y-%m-%d %H:%M:%S GMT%z') if organization.settings[:mm_lite_api_status].present? && organization.settings[:mm_lite_api_status] == 'ONBOARDED'
          organization.save
        end
      end
    end
    true
  end

  def delete_channel_cache(channel)
    settings = channel.settings.with_indifferent_access
    Services::Redis::ChannelIntegrations::Del.new(channel.id).call
    Services::Redis::ChannelIntegrations::DelByWebhook.new(channel.webhook).call
    Services::Redis::ChannelIntegrations::EmailByUsername.new(settings[:username]).del if channel.is_email?
    Services::Redis::ChannelIntegrations::FbByPageId.new(settings[:page_id]).del if channel.is_fb?
  end

  def del_org_cache(organization)
    Services::Redis::AutoResolve::ResetOrganizationChannels.new(organization.id).call
    Services::Redis::Organizations::Del.new(organization.id).call
  end

  def update_channel_division(organization_id)
    divisions = Models::Division.where(organization_id: organization_id)
    divisions.map { |division| division.__elasticsearch__.index_document refresh: true }
  end

  def remove_old_sync_message_coexistence(organization)
    Models::SyncLog.where('original_time < ? and organization_id = ?', 6.month.ago, organization.id).try(:destroy_all)
  end
end
