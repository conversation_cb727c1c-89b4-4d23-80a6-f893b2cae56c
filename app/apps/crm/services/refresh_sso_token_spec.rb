# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Crm::Services::RefreshSsoToken do
  describe '#call' do
    let(:new_token) do
      Hashie::Mash.new(
        access_token:  'new-chat-token',
        refresh_token: 'new-chat-refresh-token',
        expires_in:    3600,
      )
    end
    let(:old_refresh_token) { 'old-chat-refresh-token' }

    subject { described_class.new(token_attr: new_token, old_refresh_token: old_refresh_token).call }

    context 'when request is successful' do
      let(:success_response) {
        {
          'status' => 'Success'
        }
      }

      before do
        allow_any_instance_of(Repositories::Http::RequestMethods).to receive(:post)
        allow_any_instance_of(Repositories::AbstractHttp)
          .to receive(:parse_response)
          .and_return(Dry::Monads::Success(Hashie::Mash.new(success_response)))
      end

      it 'returns success' do
        expect(subject).to be_success
        expect(subject.success).to eq(success_response)
      end
    end

    context 'when request fails' do
      let(:error_response) { { 'status' => 'Bad request' } }

      before do
        allow_any_instance_of(Repositories::Http::RequestMethods).to receive(:post)
        allow_any_instance_of(Repositories::AbstractHttp)
          .to receive(:parse_response)
          .and_return(Dry::Monads::Failure(Hashie::Mash.new(error_response)))
      end

      it 'returns failure' do
        expect(subject).to be_failure
        expect(subject.failure).to eq(error_response)
      end
    end
  end
end
