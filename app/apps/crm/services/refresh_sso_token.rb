# frozen_string_literal: true

class Crm::Services::RefreshSsoToken < Repositories::AbstractHttp
  def initialize(token_attr:, old_refresh_token:)
    @base_url   = ENV['CRM_BASE_URL']
    @token_attr = token_attr
    @old_refresh_token = old_refresh_token
  end

  def call
    request = post(path: '/api/mobile/v2.8/sessions/update_sso_refresh_token', body: request_payload)
    parse_response(request)
  end

  private

  def request_payload
    {
      token:             @token_attr[:access_token],
      refresh_token:     @token_attr[:refresh_token],
      old_refresh_token: @old_refresh_token,
      expires_in:        @token_attr[:expires_in]
    }
  end
end
