# frozen_string_literal: true

class Billings::Repositories::V1::WaConversationLogs::GetBcIdIsNull < Billings::Repositories::V1::AbstractRepository
  def initialize(start_at:, end_at:)
    @start_at = start_at
    @end_at = end_at
  end

  def call
    logs = switch_replica_billing_db do
      execute_query(build_query, @start_at, @end_at)
    end
    return Success [] if logs.count.eql?(0)

    Success build(logs)
  end

  private

  def build_query
    <<~SQL
      SELECT id, organization_id, conversation_id, total_price
      FROM wa_conversation_logs
      WHERE message_broadcast_id IS NULL
      AND origin_type = 'BI'
      AND created_at BETWEEN ? AND ?
      AND is_auto_deduct = true
      AND credited_to != 'free'
    SQL
  end

  def build logs
    result = []
    logs.each do |log|
      result << Hashie::Mash.new(
                  {
                    id:              log['id'],
                    organization_id: log['organization_id'],
                    conversation_id: log['conversation_id'],
                    total_price:     log['total_price']
                  }
      )
    end

    result
  end
end
