# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SupportTools::Services::FeatureFlag do
  let(:company_id) { 1 }
  let!(:organization) { create :organization, id: 42, settings: { :custom_agent_allocation => 'round_robin', 'auto_agent_allocation' => { "assignment_type": 'default' } }, company_id: company_id }

  describe '#initialize' do
    context 'when company_id is valid' do
      it 'does not raise error' do
        expect { described_class.new(company_id: company_id) }.not_to raise_error
      end
    end

    context 'when company_id is invalid' do
      before { allow(Models::Organization).to receive(:find_by).with(company_id: company_id).and_return(nil) }
      it 'raises StandardError' do
        expect { described_class.new(company_id: company_id) }.to raise_error(StandardError, "Company ID #{company_id} is not valid")
      end
    end
  end

  describe '#switch' do
    subject(:feature_flag) { described_class.new(company_id: company_id) }

    context 'when feature not handled' do
      it 'raises ArgumentError' do
        expect { feature_flag.switch(feature: 'custom_chatbot', value: true) }.to raise_error(ArgumentError, 'Unknown feature: custom_chatbot')
      end
    end

    context "when feature is 'wa_call'" do
      it 'calls WaCall::UpdateCallSettings with correct args' do
        wa_call_service = class_double('Services::WaCall::UpdateCallSettings').as_stubbed_const
        expect(wa_call_service).to receive(:new).with(organization, true).and_return(double(call: true))
        feature_flag.switch(feature: 'wa_call', value: true)
      end
    end

    context "when feature is 'wa_flow'" do
      before do
        Services::Preference.new.add :enable_wa_flow_specific, title: 'flag to enable wa flow specific organization', extra: {}, target: 'feature', author: '<EMAIL>'
        Services::Preference.new.enable :enable_wa_flow_specific
      end

      it 'enables wa_flow when value is true' do
        feature_flag.switch(feature: 'wa_flow', value: true)
        flag_status = Services::Preference.new.enabled?(:enable_wa_flow_specific, organization_id: organization.id)
        expect(flag_status).to be_truthy
      end

      it 'disables wa_flow when value is false' do
        feature_flag.switch(feature: 'wa_flow', value: false)
        flag_status = Services::Preference.new.enabled?(:enable_wa_flow_specific, organization_id: organization.id)
        expect(flag_status).to be_falsey
      end
    end

    context "when feature is 'aaa_assign_new_room'" do
      it 'not change other settings value' do
        feature_flag.switch(feature: 'aaa_assign_new_room', value: true)
        organization.reload
        expect(organization.settings).to include('custom_agent_allocation' => 'round_robin')
        expect(organization.auto_agent_allocation).to include('assignment_type' => 'default')
      end

      context 'when value is true' do
        it 'enable auto_agent_allocation - assign new room' do
          feature_flag.switch(feature: 'aaa_assign_new_room', value: true)
          organization.reload
          expect(organization.auto_agent_allocation).to include('assign_new_room' => true)
        end
      end
      context 'when value is false' do
        it 'disable auto_agent_allocation - assign new room' do
          feature_flag.switch(feature: 'aaa_assign_new_room', value: false)
          organization.reload
          expect(organization.auto_agent_allocation).to include('assign_new_room' => false)
        end
      end
      context 'when there is a database error' do
        before { allow_any_instance_of(Models::Organization).to receive(:update!).and_raise(ActiveRecord::RecordInvalid) }
        it 'raises the error' do
          expect { feature_flag.switch(feature: 'aaa_assign_new_room', value: true) }.to raise_error(ActiveRecord::RecordInvalid)
        end
      end
    end
  end
end
