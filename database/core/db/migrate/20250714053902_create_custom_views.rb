# frozen_string_literal: true

class CreateCustomViews < ActiveRecord::Migration[6.1]
  def change
    create_table :custom_views, id: :uuid do |t|
      t.uuid :organization_id, null: false
      t.uuid :user_id, null: true
      t.string :name
      t.string :icon
      t.jsonb :filters, null: false, default: {}
      t.timestamps
    end

    add_index :custom_views, [:organization_id, :user_id], name: :idx_custom_view_org_user_id
  end
end
