# frozen_string_literal: true

class CreateCustomViewDataChanges < ActiveRecord::Migration[6.1]
  def change
    create_table :custom_view_data_changes, id: :uuid do |t|
      t.uuid :custom_view_id, null: false
      t.uuid :actor_id, null: true
      t.string :name
      t.string :icon
      t.jsonb :filters, null: false, default: {}
      t.timestamps
    end

    add_index :custom_view_data_changes, :custom_view_id, name: :idx_custviewdatchages_custviewid
  end
end
